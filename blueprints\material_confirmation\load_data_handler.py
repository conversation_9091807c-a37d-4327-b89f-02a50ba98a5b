import datetime
import random
from flask import Blueprint, render_template, jsonify, request
from db_config import get_db_connection
import mysql.connector

Material_Sample_Confirmation_Form_load_data_bp = Blueprint('Material_Sample_Confirmation_Form_load_data_bp', __name__)

# 动态数据接口（返回JSON）
@Material_Sample_Confirmation_Form_load_data_bp.route('/<sample_id>')
def load_sample_data(sample_id):
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询基本信息
        cursor.execute("""
            SELECT report_code, supplier, inspection_date, sample_count, inspector,
                   material_number, graph_number, material_name, drawing_version,
                   material_texture, surface_processing, sample_status, other_textbox,
                   final_judgment, opinion, review
            FROM material_sample_confirmation_form
            WHERE report_code = %s
        """, (sample_id,))
        base_info = cursor.fetchone()
        
        if not base_info:
            return jsonify({"error": "未找到对应报告数据"}), 404
        
        # 查询尺寸数据
        cursor.execute("""
            SELECT 
                size_number, 
                COALESCE(position, '') as position, 
                COALESCE(value, '') as value, 
                COALESCE(min_value, '') as min_value, 
                COALESCE(max_value, '') as max_value,
                COALESCE(measure_1, '') as measure_1, 
                COALESCE(measure_2, '') as measure_2, 
                COALESCE(measure_3, '') as measure_3, 
                COALESCE(measure_4, '') as measure_4, 
                COALESCE(measure_5, '') as measure_5,
                COALESCE(NULLIF(check_result, ''), '/') as check_result, 
                COALESCE(note, '') as note
            FROM material_sample_size_data
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY size_number
        """, (sample_id,))
        size_data = cursor.fetchall()
        
        # 创建尺寸数据字典，确保有30行数据
        size_dict = {}
        for row in size_data:
            size_dict[row['size_number']] = row
        
        # 确保有30行数据，缺失的用空值填充
        for i in range(1, 31):
            if i not in size_dict:
                size_dict[i] = {
                    'size_number': i,
                    'position': '',
                    'value': '',
                    'min_value': '',
                    'max_value': '',
                    'measure_1': '',
                    'measure_2': '',
                    'measure_3': '',
                    'measure_4': '',
                    'measure_5': '',
                    'check_result': '/',
                    'note': ''
                }

        # 计算有数据的尺寸行数
        size_row_count = len([row for row in size_dict.values() if any([
            row['position'], row['value'], row['min_value'], row['max_value'],
            row['measure_1'], row['measure_2'], row['measure_3'], row['measure_4'], row['measure_5']
        ])])

        # 提取尺寸1-3的字段（为了兼容原始模板）
        size_1 = size_dict.get(1, {})
        size_2 = size_dict.get(2, {})
        size_3 = size_dict.get(3, {})
        
        # 查询外观检查数据
        cursor.execute("""
            SELECT check_number, check_result, note
            FROM material_sample_appearance
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY check_number
        """, (sample_id,))
        appearance_data = cursor.fetchall()
        
        # 创建外观检查数据字典，确保有18行数据
        appearance_dict = {}
        for row in appearance_data:
            appearance_dict[row['check_number']] = row
        
        # 确保有18行数据，缺失的用空值填充
        for i in range(1, 19):
            if i not in appearance_dict:
                appearance_dict[i] = {
                    'check_number': i,
                    'check_result': '/',
                    'note': ''
                }
        
        # 查询功能检查数据
        cursor.execute("""
            SELECT check_number, check_result, note, burnin_info, electrical_info,
                   tests_info, other_test, other_info
            FROM material_sample_function
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY check_number
        """, (sample_id,))
        function_data = cursor.fetchall()
        
        # 查询问题记录和图片路径
        cursor.execute("""
            SELECT q.question_number, q.question_text, q.image_path
            FROM material_sample_questions q
            WHERE q.form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY q.question_number
        """, (sample_id,))
        
        questions = []
        question_images = {}
        valid_question_numbers = []
        
        for row in cursor.fetchall():
            question_number = row['question_number']
            question_text = row['question_text']
            image_path = row['image_path']
            
            # 如果有问题文本，添加到问题列表
            if question_text and question_text.strip():
                questions.append({
                    'number': question_number,
                    'text': question_text
                })
                valid_question_numbers.append(question_number)
            
            # 如果有图片路径，添加到图片字典
            if image_path and image_path.strip():
                question_images[question_number] = image_path
        
        # 确保问题列表有18个元素，缺失的用空值填充
        question_dict = {}
        for q in questions:
            question_dict[q['number']] = q['text']
        
        for i in range(1, 19):
            if i not in question_dict:
                question_dict[i] = ''
        
        # 创建功能检查数据字典，确保有18行数据
        function_dict = {}
        for row in function_data:
            function_dict[row['check_number']] = row
        
        # 确保有18行数据，缺失的用空值填充
        for i in range(1, 19):
            if i not in function_dict:
                function_dict[i] = {
                    'check_number': i,
                    'check_result': '/',
                    'note': '',
                    'burnin_info': '',
                    'electrical_info': '',
                    'tests_info': '',
                    'other_test': '',
                    'other_info': ''
                }
        
        return render_template(
            'material_confirmation/Material_Sample_Confirmation_Form_load_data.html',
            report_code=base_info['report_code'],
            supplier=base_info['supplier'],
            inspection_date=base_info['inspection_date'],
            sample_count=base_info['sample_count'] if base_info['sample_count'] is not None else '',
            inspector=base_info['inspector'],
            material_number=base_info['material_number'],
            graph_number=base_info['graph_number'],
            material_name=base_info['material_name'],
            drawing_version=base_info['drawing_version'],
            material_texture=base_info['material_texture'],
            surface_processing=base_info['surface_processing'],
            sample_status_list = base_info['sample_status'].split(',') if base_info['sample_status'] else [],
            other_textbox_value = base_info['other_textbox'],
            final_judgment=base_info['final_judgment'],
            opinion=base_info['opinion'],
            review=base_info['review'],
            size_data=size_dict,
            size_row_count=size_row_count,
            appearance_data=appearance_dict,
            function_data=function_dict,
            questions=question_dict,
            question_images=question_images,
            valid_question_numbers=valid_question_numbers,

            # 尺寸1-3的字段
            size_1_position=size_1.get('position', ''),
            size_1_value=size_1.get('value', ''),
            size_1_min=size_1.get('min_value', ''),
            size_1_max=size_1.get('max_value', ''),
            size_1_measure_1=size_1.get('measure_1', ''),
            size_1_measure_2=size_1.get('measure_2', ''),
            size_1_measure_3=size_1.get('measure_3', ''),
            size_1_measure_4=size_1.get('measure_4', ''),
            size_1_measure_5=size_1.get('measure_5', ''),
            size_1_check=size_1.get('check_result', ''),
            size_1_note=size_1.get('note', ''),

            size_2_position=size_2.get('position', ''),
            size_2_value=size_2.get('value', ''),
            size_2_min=size_2.get('min_value', ''),
            size_2_max=size_2.get('max_value', ''),
            size_2_measure_1=size_2.get('measure_1', ''),
            size_2_measure_2=size_2.get('measure_2', ''),
            size_2_measure_3=size_2.get('measure_3', ''),
            size_2_measure_4=size_2.get('measure_4', ''),
            size_2_measure_5=size_2.get('measure_5', ''),
            size_2_check=size_2.get('check_result', ''),
            size_2_note=size_2.get('note', ''),

            size_3_position=size_3.get('position', ''),
            size_3_value=size_3.get('value', ''),
            size_3_min=size_3.get('min_value', ''),
            size_3_max=size_3.get('max_value', ''),
            size_3_measure_1=size_3.get('measure_1', ''),
            size_3_measure_2=size_3.get('measure_2', ''),
            size_3_measure_3=size_3.get('measure_3', ''),
            size_3_measure_4=size_3.get('measure_4', ''),
            size_3_measure_5=size_3.get('measure_5', ''),
            size_3_check=size_3.get('check_result', ''),
            size_3_note=size_3.get('note', ''),

            # 功能检查数据
            function_1_check=function_dict.get(1, {}).get('check_result', '/'),
            function_1_note=function_dict.get(1, {}).get('note', ''),
            function_2_check=function_dict.get(2, {}).get('check_result', '/'),
            function_2_note=function_dict.get(2, {}).get('note', ''),
            function_3_check=function_dict.get(3, {}).get('check_result', '/'),
            function_3_note=function_dict.get(3, {}).get('note', ''),
            function_4_check=function_dict.get(4, {}).get('check_result', '/'),
            function_4_note=function_dict.get(4, {}).get('note', ''),
            function_4_burnin=function_dict.get(4, {}).get('burnin_info', ''),
            function_4_electrical=function_dict.get(4, {}).get('electrical_info', ''),
            function_5_check=function_dict.get(5, {}).get('check_result', '/'),
            function_5_note=function_dict.get(5, {}).get('note', ''),
            function_5_tests=function_dict.get(5, {}).get('tests_info', '').split(',') if function_dict.get(5, {}).get('tests_info') else [],
            function_5_other_test=function_dict.get(5, {}).get('other_test', ''),
            function_6_check=function_dict.get(6, {}).get('check_result', '/'),
            function_6_note=function_dict.get(6, {}).get('note', ''),
            function_6_other=function_dict.get(6, {}).get('other_info', '')
        )
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


@Material_Sample_Confirmation_Form_load_data_bp.route('/submit', methods=['POST'])
def submit_form():
    try:
        report_code = f"SC{datetime.datetime.now().strftime('%Y%m%d')}-{random.randint(100,999)}"
        sample_status = request.form.getlist('sample_status[]')  # 获取所有选中的复选框值
        other_textbox = request.form.get('other_textbox', '')    # 获取其他文本框的内容

        # 将复选框值转为字符串存储
        sample_status_str = ','.join(sample_status) if sample_status else ''
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""INSERT INTO material_sample_confirmation_form
            (report_code, supplier, inspection_date, sample_count, inspector,
             material_number, graph_number, material_name, drawing_version,
             sample_status, other_textbox,
             material_texture, surface_processing,
             final_judgment, opinion, review)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            report_code,
            request.form['supplier'],
            request.form['inspection_date'],
            request.form['sample_count'],
            request.form['inspector'],
            request.form['material_number'],
            request.form['graph_number'],
            request.form['material_name'],
            request.form['drawing_version'],
            sample_status_str,
            other_textbox,
            request.form['material_texture'],
            request.form['surface_processing'],
            request.form.get('final_judgment', ''),
            request.form.get('opinion', ''),
            request.form.get('review', '')
        ))
        form_id = cursor.lastrowid

        # 保存尺寸数据
        for i in range(1, 31):
            position = request.form.get(f'size_{i}_position', '')
            value = request.form.get(f'size_{i}_value', '')
            min_val = request.form.get(f'size_{i}_min', '')
            max_val = request.form.get(f'size_{i}_max', '')
            measures = [
                request.form.get(f'size_{i}_measure_{j}', '') for j in range(1,6)
            ]
            check_result = request.form.get(f'size_{i}_check', '')
            note = request.form.get(f'size_{i}_note', '')

            cursor.execute("""
                INSERT INTO material_sample_size_data
                (form_id, size_number, position, value, min_value, max_value,
                 measure_1, measure_2, measure_3, measure_4, measure_5,
                 check_result, note)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                form_id, i, position, value, min_val, max_val,
                *measures, check_result, note
            ))

        # 保存外观检查
        for i in range(1,19):
            check_result = request.form.get(f'appearance_{i}_check', '')
            note = request.form.get(f'appearance_{i}_note', '')
            cursor.execute("""
                INSERT INTO material_sample_appearance
                (form_id, check_number, check_result, note)
                VALUES (%s, %s, %s, %s)
            """, (form_id, i, check_result, note))

        # 保存功能检查
        for i in range(1,7):
            check_result = request.form.get(f'function_{i}_check', '')
            note = request.form.get(f'function_{i}_note', '')
            burnin = request.form.get(f'function_{i}_burnin', '')
            electrical = request.form.get(f'function_{i}_electrical', '')
            tests_list = request.form.getlist(f'function_{i}_tests[]')
            tests = ','.join(tests_list)
            other_test = request.form.get(f'function_{i}_other_test', '')
            other_info = request.form.get(f'function_{i}_other', '')
            cursor.execute("""
                INSERT INTO material_sample_function
                (form_id, check_number, check_result, note, burnin_info,
                electrical_info, tests_info, other_test, other_info)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (form_id, i, check_result, note, burnin, electrical, tests, other_test, other_info))

        # 保存问题记录
        for i in range(1,19):
            question_text = request.form.get(f'question_{i}', '')
            image_path = request.form.get(f'question_{i}_image', '')
            cursor.execute("""
                INSERT INTO material_sample_questions
                (form_id, question_number, question_text, image_path)
                VALUES (%s, %s, %s, %s)
            """, (form_id, i, question_text, image_path))

        conn.commit()
        return jsonify({"report_code": report_code}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
