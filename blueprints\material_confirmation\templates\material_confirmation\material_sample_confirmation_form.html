<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>物料样板确认书</title>
    <style>
        .image-upload-cell 
        .image-upload-box{
            height: 120px;
            position: relative;
        }
        body {
            width: 1000px;  /* 根据实际需求调整 */
            margin: 0 auto;
            font-family: "Microsoft YaHei"; 
            font-size: 12px;
        }
        table {
            table-layout: fixed;  /* 固定表格布局 */
            width: 100%;
            max-width: 1000px;
            border-collapse: collapse;
            border: 2px solid #000000;
            border-spacing: 0; /* 显式声明 */
        }
        td input[type="text"] {
            width: 100%;
            box-sizing: border-box;
            padding: 4px 3px;
            margin: 0;
            display: block;
            height: 100%;
            border: none;
        }
        td input[type="text"]:focus {
            outline: 2px solid #000000;
            border: 2px solid #000000;
            border-radius: 5px;
        }
        td input[type="text"] {
            height: 100%;
            box-sizing: border-box;
            padding: 4px 3px;
            margin: 0;
            display: block;
        }
        td input[type="text"]:focus {
            outline: 2px solid #000000;
            border-radius: 5px;
        }
        td {
            border: 1px solid #000000;
            padding: 2px 1px;
            vertical-align: middle;
            line-height: 1.2;
        }
        .main-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            background: #D9D9D9;
            padding: 8px 0;
        }
        .module-title {
            background: #F2F2F2;
            font-weight: bold;
            padding-left: 4px;
        }
        .sub-header {
            background: #E6E6E6;
        }
        .col-no { width: 35px; }
        .col-pos { width: 90px; }
        .col-value { width: 55px; }
        .col-measure { width: 35px; }
        .col-check { width: 70px; }
        .col-note { width: 100px; }
        .align-center { text-align: center; }
        .align-left { text-align: left; padding-left: 4px; }
        .check-box {
            width: 14px;
            height: 14px;
            border: 1px solid #000;
            display: inline-block;
            margin: 0 5px;
            vertical-align: middle;
        }
        input[type="checkbox"] {
            margin: 0 3px 0 10px;
        }

        .custom-row-height {
            width: 100%; 
            border-collapse: collapse;
            table-layout: fixed;
            border: none;
            border-spacing: 0; /* 显式声明 */
            height: 10px; /* 根据需要调整高度 */
        }
        
        /* 图片上传相关样式 */
        .image-upload-container {
            position: relative;
            width: 100%;
            height: 120px;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background-color: #f9f9f9;
        }
        
        .image-upload-container:hover {
            border-color: #999;
            background-color: #f0f0f0;
        }
        
        .image-preview {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .upload-text {
            color: #666;
            font-size: 12px;
        }
        
        .image-upload-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
        
        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        /* 表单控制按钮区域 */
        .form-controls {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            border-top: 2px solid #000;
        }
    </style>
</head>
<body>
    <form id="materialForm" action="{{ url_for('material_confirmation.submit_confirmation') }}" method="post">
        <table>
            <tr>
                <td colspan="8" class="main-title">物料样板确认书</td>
            </tr>
            
            <!-- 基本信息部分 -->
            <tr>
                <td class="module-title align-left" style="width: 80px;">供应商</td>
                <td style="width: 120px;"><input type="text" name="supplier" id="supplier"></td>
                <td class="module-title align-left" style="width: 80px;">送检日期</td>
                <td style="width: 120px;"><input type="date" name="inspection_date" id="inspection_date"></td>
                <td class="module-title align-left" style="width: 80px;">样板数量</td>
                <td style="width: 80px;"><input type="number" name="sample_count" id="sample_count"></td>
                <td class="module-title align-left" style="width: 80px;">检验员</td>
                <td style="width: 120px;"><input type="text" name="inspector" id="inspector"></td>
            </tr>
            
            <tr>
                <td class="module-title align-left">物料料号</td>
                <td><input type="text" name="material_number" id="material_number"></td>
                <td class="module-title align-left">图号</td>
                <td><input type="text" name="graph_number" id="graph_number"></td>
                <td class="module-title align-left">物料名称</td>
                <td colspan="3"><input type="text" name="material_name" id="material_name"></td>
            </tr>
            
            <tr>
                <td class="module-title align-left">图纸版本</td>
                <td><input type="text" name="drawing_version" id="drawing_version"></td>
                <td class="module-title align-left">材质</td>
                <td><input type="text" name="material_texture" id="material_texture"></td>
                <td class="module-title align-left">表面处理</td>
                <td colspan="3"><input type="text" name="surface_processing" id="surface_processing"></td>
            </tr>
            
            <tr>
                <td class="module-title align-left">样板状态</td>
                <td colspan="7">
                    <label><input type="checkbox" name="sample_status" value="首件"> 首件</label>
                    <label><input type="checkbox" name="sample_status" value="量产前样板"> 量产前样板</label>
                    <label><input type="checkbox" name="sample_status" value="工程变更"> 工程变更</label>
                    <label><input type="checkbox" name="sample_status" value="其他"> 其他</label>
                    <input type="text" name="other_textbox" id="other_textbox" placeholder="请说明其他情况" style="width: 200px; margin-left: 10px;">
                </td>
            </tr>
        </table>
        
        <!-- 问题点记录部分 -->
        <table style="margin-top: 10px;">
            <tr>
                <td colspan="3" class="module-title">问题点记录</td>
            </tr>
            <tr class="sub-header">
                <td class="align-center" style="width: 50px;">序号</td>
                <td class="align-center" style="width: 400px;">问题描述</td>
                <td class="align-center" style="width: 150px;">图片</td>
            </tr>
            <tbody id="questionsTableBody">
                <!-- 问题点行将通过JavaScript动态添加 -->
            </tbody>
        </table>
        
        <div style="text-align: center; margin: 10px 0;">
            <button type="button" class="btn btn-secondary" onclick="addQuestionRow()">添加问题点</button>
        </div>
        
        <!-- 最终判定部分 -->
        <table style="margin-top: 10px;">
            <tr>
                <td class="module-title align-left" style="width: 100px;">最终判定</td>
                <td style="width: 200px;">
                    <label><input type="radio" name="final_judgment" value="合格"> 合格</label>
                    <label><input type="radio" name="final_judgment" value="不合格"> 不合格</label>
                    <label><input type="radio" name="final_judgment" value="让步接收"> 让步接收</label>
                </td>
                <td class="module-title align-left" style="width: 80px;">意见</td>
                <td><textarea name="opinion" id="opinion" style="width: 100%; height: 60px; border: none; resize: none;"></textarea></td>
            </tr>
            
            <tr>
                <td class="module-title align-left">审核</td>
                <td colspan="3">
                    <label><input type="radio" name="review" value="同意"> 同意</label>
                    <label><input type="radio" name="review" value="不同意"> 不同意</label>
                </td>
            </tr>
        </table>
        
        <!-- 表单控制按钮 -->
        <div class="form-controls">
            <button type="button" class="btn btn-success" onclick="submitForm()">提交表单</button>
            <button type="button" class="btn btn-secondary" onclick="resetForm()">重置表单</button>
            <button type="button" class="btn btn-secondary" onclick="window.close()">关闭</button>
        </div>
    </form>

    <script>
        let questionCounter = 0;

        // 添加问题点行
        function addQuestionRow() {
            questionCounter++;
            const tbody = document.getElementById('questionsTableBody');
            const row = document.createElement('tr');

            row.innerHTML = `
                <td class="align-center">${questionCounter}</td>
                <td>
                    <textarea name="question_text_${questionCounter}"
                              style="width: 100%; height: 60px; border: none; resize: none;"
                              placeholder="请描述问题..."></textarea>
                </td>
                <td>
                    <div class="image-upload-container" onclick="triggerFileInput(${questionCounter})">
                        <input type="file"
                               id="image_input_${questionCounter}"
                               class="image-upload-input"
                               accept="image/*"
                               onchange="handleImageUpload(${questionCounter}, this)">
                        <div class="upload-text" id="upload_text_${questionCounter}">点击上传图片</div>
                        <img id="image_preview_${questionCounter}"
                             class="image-preview"
                             style="display: none;">
                    </div>
                </td>
            `;

            tbody.appendChild(row);
        }

        // 触发文件选择
        function triggerFileInput(questionNumber) {
            document.getElementById(`image_input_${questionNumber}`).click();
        }

        // 处理图片上传
        function handleImageUpload(questionNumber, input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                const reader = new FileReader();

                reader.onload = function(e) {
                    const preview = document.getElementById(`image_preview_${questionNumber}`);
                    const uploadText = document.getElementById(`upload_text_${questionNumber}`);

                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    uploadText.style.display = 'none';
                };

                reader.readAsDataURL(file);
            }
        }

        // 提交表单
        function submitForm() {
            const formData = new FormData();

            // 收集基本信息
            const basicFields = [
                'supplier', 'inspection_date', 'sample_count', 'inspector',
                'material_number', 'graph_number', 'material_name',
                'drawing_version', 'material_texture', 'surface_processing',
                'other_textbox', 'opinion'
            ];

            basicFields.forEach(field => {
                const element = document.getElementById(field);
                if (element) {
                    formData.append(field, element.value);
                }
            });

            // 收集样板状态（复选框）
            const sampleStatusCheckboxes = document.querySelectorAll('input[name="sample_status"]:checked');
            const sampleStatusValues = Array.from(sampleStatusCheckboxes).map(cb => cb.value);
            formData.append('sample_status', sampleStatusValues.join(', '));

            // 收集最终判定（单选框）
            const finalJudgment = document.querySelector('input[name="final_judgment"]:checked');
            if (finalJudgment) {
                formData.append('final_judgment', finalJudgment.value);
            }

            // 收集审核（单选框）
            const review = document.querySelector('input[name="review"]:checked');
            if (review) {
                formData.append('review', review.value);
            }

            // 收集问题点数据
            const questions = [];
            for (let i = 1; i <= questionCounter; i++) {
                const questionText = document.querySelector(`textarea[name="question_text_${i}"]`);
                const imageInput = document.getElementById(`image_input_${i}`);

                if (questionText && questionText.value.trim()) {
                    const questionData = {
                        question_number: i,
                        question_text: questionText.value.trim(),
                        image_path: ''
                    };

                    // 如果有图片，添加到FormData中
                    if (imageInput && imageInput.files[0]) {
                        formData.append(`question_image_${i}`, imageInput.files[0]);
                        questionData.image_path = `question_image_${i}`;
                    }

                    questions.push(questionData);
                }
            }

            // 将问题点数据转换为JSON字符串
            formData.append('questions', JSON.stringify(questions));

            // 发送数据到服务器
            fetch('{{ url_for("material_confirmation.submit_confirmation") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('表单提交成功！报告编码：' + data.report_code);
                    // 可以选择关闭窗口或重定向
                    window.close();
                } else {
                    alert('提交失败：' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('提交失败，请检查网络连接');
            });
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置表单吗？所有数据将被清除。')) {
                document.getElementById('materialForm').reset();
                document.getElementById('questionsTableBody').innerHTML = '';
                questionCounter = 0;
            }
        }

        // 页面加载时添加一个默认的问题点行
        document.addEventListener('DOMContentLoaded', function() {
            addQuestionRow();
        });
    </script>
</body>
</html>
