from flask import render_template, request, jsonify
from . import material_confirmation_bp
from db_config import get_db_connection
import re
import json
import os
import time
import uuid
import shutil
import threading
import random
from datetime import datetime
from werkzeug.utils import secure_filename
from threading import Lock

@material_confirmation_bp.route('/')
def index():
    """物料确认清单首页"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # 获取搜索参数
    supplier = request.args.get('supplier')
    material_number = request.args.get('material_number')
    material_name = request.args.get('material_name')
    inspection_date = request.args.get('inspection_date')
    created_at = request.args.get('created_at')
    inspector = request.args.get('inspector')
    final_judgment = request.args.get('final_judgment')
    
    # 构建基础查询
    query = """
        SELECT 
            m.id, m.report_code, m.supplier, m.material_number, m.material_name, 
            DATE_FORMAT(m.inspection_date, '%Y-%m-%d') as inspection_date, 
            DATE_FORMAT(m.created_at, '%Y-%m-%d') as created_at, 
            m.inspector, m.final_judgment, m.opinion,
            (SELECT JSON_ARRAYAGG(JSON_OBJECT('question_number', q.question_number, 'question_text', q.question_text))
             FROM material_sample_questions q 
             WHERE q.form_id = m.id) AS questions
        FROM material_sample_confirmation_form m
    """
    
    conditions = []
    params = []

    if supplier:
        conditions.append("m.supplier LIKE %s")
        params.append(f"%{supplier}%")
    if material_number:
        conditions.append("m.material_number LIKE %s")
        params.append(f"%{material_number}%")
    if material_name:
        conditions.append("m.material_name LIKE %s")
        params.append(f"%{material_name}%")
    if inspection_date:
        conditions.append("DATE_FORMAT(m.inspection_date, '%Y-%m-%d') = %s")
        params.append(inspection_date)
    if created_at:
        conditions.append("DATE_FORMAT(m.created_at, '%Y-%m-%d') = %s")
        params.append(created_at)
    if inspector:
        conditions.append("m.inspector LIKE %s")
        params.append(f"%{inspector}%")
    if final_judgment:
        conditions.append("m.final_judgment LIKE %s")
        params.append(f"%{final_judgment}%")

    if conditions:
        query += " WHERE " + " AND ".join(conditions)

    query += " ORDER BY inspection_date DESC"

    cursor.execute(query, tuple(params))
    material_samples = cursor.fetchall()
    
    # 为每个物料样板获取问题点并按序号排序
    for sample in material_samples:
        cursor.execute("""
            SELECT question_number, question_text 
            FROM material_sample_questions 
            WHERE form_id = %s 
            ORDER BY question_number
        """, (sample['id'],))
        questions = cursor.fetchall()
        sample['questions'] = questions
    
    cursor.close()
    conn.close()
    return render_template('material_confirmation/confirmation_list.html', material_samples=material_samples)

@material_confirmation_bp.route('/search')
def search_material_samples():
    """搜索物料确认记录"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # 获取所有搜索参数
    search_fields = request.args.getlist('search_field')
    search_values = request.args.getlist('search_value')
    
    if not search_fields or not search_values or len(search_fields) != len(search_values):
        return jsonify({'error': 'Missing search parameters'}), 400
    
    # 定义允许搜索的字段白名单
    allowed_fields = ['supplier', 'material_number', 'material_name', 'inspection_date', 
                     'created_at', 'inspector', 'final_judgment']
        
    # 构建查询条件和参数
    conditions = []
    params = []
    
    for field, value in zip(search_fields, search_values):
        if field not in allowed_fields:
            continue
            
        if not value or not value.strip():
            continue
            
        # 日期字段特殊处理
        if field in ['inspection_date', 'created_at']:
            # 处理'4.7'这样的简写日期格式
            if re.search(r'^\d{1,2}\.\d{1,2}$', value):
                current_year = datetime.now().year
                month, day = value.split('.')
                value = f"{current_year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'4-7'这样的简单日期格式
            elif re.search(r'^\d{1,2}-\d{1,2}$', value):
                current_year = datetime.now().year
                month, day = value.split('-')
                value = f"{current_year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'2025.4.7'这样的完整日期格式
            elif re.search(r'^\d{4}\.\d{1,2}\.\d{1,2}$', value):
                year, month, day = value.split('.')
                value = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'2025-4-7'这样的格式
            elif re.search(r'^\d{4}-\d{1,2}-\d{1,2}$', value):
                year, month, day = value.split('-')
                value = f"{year}-{month.zfill(2)}-{day.zfill(2)}"

            conditions.append(f"DATE_FORMAT({field}, '%Y-%m-%d') = %s")
            params.append(value)
        else:
            conditions.append(f"{field} LIKE %s")
            params.append(f"%{value}%")
    
    query = """
        SELECT 
            m.id, m.report_code, m.supplier, m.material_number, m.material_name, 
            DATE_FORMAT(m.inspection_date, '%Y-%m-%d') as inspection_date, 
            DATE_FORMAT(m.created_at, '%Y-%m-%d') as created_at, 
            m.inspector, m.final_judgment, m.opinion,
            (SELECT JSON_ARRAYAGG(JSON_OBJECT('question_number', q.question_number, 'question_text', q.question_text))
             FROM material_sample_questions q 
             WHERE q.form_id = m.id) AS questions
        FROM material_sample_confirmation_form m
    """
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    cursor.execute(query, tuple(params))
    results = cursor.fetchall()
    
    # 为每个结果获取问题点并按序号排序
    for result in results:
        cursor.execute("""
            SELECT question_number, question_text 
            FROM material_sample_questions 
            WHERE form_id = %s 
            ORDER BY question_number
        """, (result['id'],))
        questions = cursor.fetchall()
        result['questions'] = questions
    
    # 确保返回的是数组格式
    if not isinstance(results, list):
        results = [results]
    
    cursor.close()
    conn.close()
    return jsonify({'data': results})

@material_confirmation_bp.route('/new')
def new_confirmation():
    """新增物料确认页面"""
    return render_template('material_confirmation/material_sample_confirmation_form.html')

@material_confirmation_bp.route('/view/<report_code>')
def view_confirmation(report_code):
    """查看物料确认详情"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # 获取基本信息
        cursor.execute("""
            SELECT * FROM material_sample_confirmation_form
            WHERE report_code = %s
        """, (report_code,))

        confirmation = cursor.fetchone()
        if not confirmation:
            return "记录不存在", 404

        # 获取问题点
        cursor.execute("""
            SELECT question_number, question_text, image_path
            FROM material_sample_questions
            WHERE form_id = %s
            ORDER BY question_number
        """, (confirmation['id'],))

        questions = cursor.fetchall()
        confirmation['questions'] = questions

        return render_template('material_confirmation/view_confirmation.html', confirmation=confirmation)

    except Exception as e:
        return f"查看失败: {str(e)}", 500
    finally:
        cursor.close()
        conn.close()

@material_confirmation_bp.route('/edit/<report_code>')
def edit_confirmation(report_code):
    """编辑物料确认页面"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # 获取基本信息
        cursor.execute("""
            SELECT * FROM material_sample_confirmation_form
            WHERE report_code = %s
        """, (report_code,))

        confirmation = cursor.fetchone()
        if not confirmation:
            return "记录不存在", 404

        # 获取问题点
        cursor.execute("""
            SELECT question_number, question_text, image_path
            FROM material_sample_questions
            WHERE form_id = %s
            ORDER BY question_number
        """, (confirmation['id'],))

        questions = cursor.fetchall()
        confirmation['questions'] = questions

        return render_template('material_confirmation/edit_confirmation.html', confirmation=confirmation)

    except Exception as e:
        return f"编辑失败: {str(e)}", 500
    finally:
        cursor.close()
        conn.close()

# 全局变量和锁
file_upload_lock = Lock()
directory_creation_lock = threading.Lock()
current_material_uuid = {}

def get_image_base_path(report_code=None):
    """获取图片存储的基础路径"""
    with directory_creation_lock:
        try:
            # 读取配置文件
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'config', 'settings.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    base_path = config.get('image_base_path', 'D:/检验系统图片/')
            else:
                base_path = 'D:/检验系统图片/'  # 默认路径

            # 确保基础路径存在
            os.makedirs(base_path, exist_ok=True)

            if not os.path.exists(base_path):
                raise Exception(f"目录创建失败，请检查权限: {base_path}")

            if report_code:
                year = datetime.now().strftime("%Y")
                month = datetime.now().strftime("%m")

                year_path = os.path.join(base_path, year)
                month_path = os.path.join(year_path, month)

                try:
                    os.makedirs(year_path, exist_ok=True)
                    os.makedirs(month_path, exist_ok=True)
                except Exception as e:
                    print(f"Failed to create year/month directories: {e}")
                    raise

                final_path = os.path.join(month_path, report_code)
                try:
                    if not os.path.exists(final_path):
                        os.makedirs(final_path, exist_ok=True)
                except Exception as e:
                    print(f"Failed to create final report directory: {e}")
                    raise

                return os.path.abspath(final_path)
            else:
                return os.path.abspath(base_path)

        except Exception as e:
            print(f"Error in get_image_base_path: {e}")
            raise Exception(f"无法创建或访问图片存储路径: {str(e)}")

def get_temp_base_path(material_number=None):
    """获取临时文件基础路径"""
    try:
        # 读取配置文件获取基础路径
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'config', 'settings.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                base_path = config.get('image_base_path', 'D:/检验系统图片/')
        else:
            base_path = 'D:/检验系统图片/'  # 默认路径

        # 创建临时上传目录
        temp_uploads_dir = os.path.join(base_path, 'images', 'temp_uploads')
        os.makedirs(temp_uploads_dir, exist_ok=True)

        if material_number:
            # 为特定物料号创建子目录
            material_dir = os.path.join(temp_uploads_dir, material_number)
            os.makedirs(material_dir, exist_ok=True)
            return material_dir

        return temp_uploads_dir

    except Exception as e:
        print(f"Error in get_temp_base_path: {e}")
        # 如果配置文件读取失败，使用默认路径
        default_temp_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'static', 'images', 'temp_uploads')
        os.makedirs(default_temp_path, exist_ok=True)

        if material_number:
            material_dir = os.path.join(default_temp_path, material_number)
            os.makedirs(material_dir, exist_ok=True)
            return material_dir

        return default_temp_path

@material_confirmation_bp.route('/submit', methods=['POST'])
def submit_confirmation():
    """提交物料确认表单"""
    try:
        # 获取表单数据
        data = request.form.to_dict()

        # 生成报告编码
        report_code = generate_report_code()

        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入基本信息
        cursor.execute("""
            INSERT INTO material_sample_confirmation_form
            (report_code, supplier, inspection_date, sample_count, inspector,
             material_number, graph_number, material_name, drawing_version,
             material_texture, surface_processing, sample_status, other_textbox,
             final_judgment, opinion, review)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            report_code,
            data.get('supplier', ''),
            data.get('inspection_date', ''),
            data.get('sample_count', 0),
            data.get('inspector', ''),
            data.get('material_number', ''),
            data.get('graph_number', ''),
            data.get('material_name', ''),
            data.get('drawing_version', ''),
            data.get('material_texture', ''),
            data.get('surface_processing', ''),
            data.get('sample_status', ''),
            data.get('other_textbox', ''),
            data.get('final_judgment', ''),
            data.get('opinion', ''),
            data.get('review', '')
        ))

        form_id = cursor.lastrowid

        # 插入问题点数据
        questions_json = data.get('questions', '[]')
        try:
            questions = json.loads(questions_json) if questions_json else []
        except:
            questions = []

        for question in questions:
            cursor.execute("""
                INSERT INTO material_sample_questions
                (form_id, question_number, question_text, image_path)
                VALUES (%s, %s, %s, %s)
            """, (
                form_id,
                question.get('question_number', 0),
                question.get('question_text', ''),
                question.get('image_path', '')
            ))

        # 插入尺寸数据
        size_data = data.get('size_data', [])
        for size in size_data:
            cursor.execute("""
                INSERT INTO material_sample_size_data
                (form_id, size_number, position, value, min_value, max_value,
                 measure_1, measure_2, measure_3, measure_4, measure_5,
                 check_result, note)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                form_id,
                size.get('size_number', 0),
                size.get('position', ''),
                size.get('value', 0),
                size.get('min_value', 0),
                size.get('max_value', 0),
                size.get('measure_1', 0),
                size.get('measure_2', 0),
                size.get('measure_3', 0),
                size.get('measure_4', 0),
                size.get('measure_5', 0),
                size.get('check_result', ''),
                size.get('note', '')
            ))

        # 插入外观检查数据
        appearance_data = data.get('appearance_data', [])
        for appearance in appearance_data:
            cursor.execute("""
                INSERT INTO material_sample_appearance
                (form_id, check_number, check_result, note, other_info)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                form_id,
                appearance.get('check_number', 0),
                appearance.get('check_result', ''),
                appearance.get('note', ''),
                appearance.get('other_info', '')
            ))

        # 插入功能检查数据
        function_data = data.get('function_data', [])
        for function in function_data:
            cursor.execute("""
                INSERT INTO material_sample_function
                (form_id, check_number, check_result, note, burnin_info,
                 electrical_info, tests_info, other_test, other_info)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                form_id,
                function.get('check_number', 0),
                function.get('check_result', ''),
                function.get('note', ''),
                function.get('burnin_info', ''),
                function.get('electrical_info', ''),
                function.get('tests_info', ''),
                function.get('other_test', ''),
                function.get('other_info', '')
            ))

        conn.commit()
        cursor.close()
        conn.close()

        return jsonify({
            'success': True,
            'message': '物料确认表单提交成功',
            'report_code': report_code
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def generate_report_code():
    """生成报告编码"""
    now = datetime.now()
    date_str = now.strftime("%Y%m%d")
    time_str = now.strftime("%H%M%S")
    random_str = str(random.randint(100, 999))
    return f"MC{date_str}{time_str}{random_str}"

@material_confirmation_bp.route('/upload_image', methods=['POST'])
def upload_image():
    """处理图片上传"""
    try:
        if 'image' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        # 检查文件类型
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp'}
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            return jsonify({'success': False, 'error': '不支持的文件类型'}), 400

        # 生成安全的文件名
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"

        # 获取临时存储路径
        temp_path = get_temp_base_path()
        file_path = os.path.join(temp_path, unique_filename)

        # 保存文件
        file.save(file_path)

        return jsonify({
            'success': True,
            'filename': unique_filename,
            'path': file_path
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@material_confirmation_bp.route('/get_temp_image/<filename>')
def get_temp_image(filename):
    """获取临时图片"""
    try:
        temp_path = get_temp_base_path()
        file_path = os.path.join(temp_path, filename)

        if os.path.exists(file_path):
            from flask import send_file
            return send_file(file_path)
        else:
            return "文件不存在", 404

    except Exception as e:
        return f"获取文件失败: {str(e)}", 500
