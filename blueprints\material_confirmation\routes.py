from flask import render_template, request, jsonify
from . import material_confirmation_bp
from db_config import get_db_connection
import re
from datetime import datetime

@material_confirmation_bp.route('/')
def index():
    """物料确认清单首页"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # 获取搜索参数
    supplier = request.args.get('supplier')
    material_number = request.args.get('material_number')
    material_name = request.args.get('material_name')
    inspection_date = request.args.get('inspection_date')
    created_at = request.args.get('created_at')
    inspector = request.args.get('inspector')
    final_judgment = request.args.get('final_judgment')
    
    # 构建基础查询
    query = """
        SELECT 
            m.id, m.report_code, m.supplier, m.material_number, m.material_name, 
            DATE_FORMAT(m.inspection_date, '%Y-%m-%d') as inspection_date, 
            DATE_FORMAT(m.created_at, '%Y-%m-%d') as created_at, 
            m.inspector, m.final_judgment, m.opinion,
            (SELECT JSON_ARRAYAGG(JSON_OBJECT('question_number', q.question_number, 'question_text', q.question_text))
             FROM material_sample_questions q 
             WHERE q.form_id = m.id) AS questions
        FROM material_sample_confirmation_form m
    """
    
    conditions = []
    params = []

    if supplier:
        conditions.append("m.supplier LIKE %s")
        params.append(f"%{supplier}%")
    if material_number:
        conditions.append("m.material_number LIKE %s")
        params.append(f"%{material_number}%")
    if material_name:
        conditions.append("m.material_name LIKE %s")
        params.append(f"%{material_name}%")
    if inspection_date:
        conditions.append("DATE_FORMAT(m.inspection_date, '%Y-%m-%d') = %s")
        params.append(inspection_date)
    if created_at:
        conditions.append("DATE_FORMAT(m.created_at, '%Y-%m-%d') = %s")
        params.append(created_at)
    if inspector:
        conditions.append("m.inspector LIKE %s")
        params.append(f"%{inspector}%")
    if final_judgment:
        conditions.append("m.final_judgment LIKE %s")
        params.append(f"%{final_judgment}%")

    if conditions:
        query += " WHERE " + " AND ".join(conditions)

    query += " ORDER BY inspection_date DESC"

    cursor.execute(query, tuple(params))
    material_samples = cursor.fetchall()
    
    # 为每个物料样板获取问题点并按序号排序
    for sample in material_samples:
        cursor.execute("""
            SELECT question_number, question_text 
            FROM material_sample_questions 
            WHERE form_id = %s 
            ORDER BY question_number
        """, (sample['id'],))
        questions = cursor.fetchall()
        sample['questions'] = questions
    
    cursor.close()
    conn.close()
    return render_template('material_confirmation/confirmation_list.html', material_samples=material_samples)

@material_confirmation_bp.route('/search')
def search_material_samples():
    """搜索物料确认记录"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # 获取所有搜索参数
    search_fields = request.args.getlist('search_field')
    search_values = request.args.getlist('search_value')
    
    if not search_fields or not search_values or len(search_fields) != len(search_values):
        return jsonify({'error': 'Missing search parameters'}), 400
    
    # 定义允许搜索的字段白名单
    allowed_fields = ['supplier', 'material_number', 'material_name', 'inspection_date', 
                     'created_at', 'inspector', 'final_judgment']
        
    # 构建查询条件和参数
    conditions = []
    params = []
    
    for field, value in zip(search_fields, search_values):
        if field not in allowed_fields:
            continue
            
        if not value or not value.strip():
            continue
            
        # 日期字段特殊处理
        if field in ['inspection_date', 'created_at']:
            # 处理'4.7'这样的简写日期格式
            if re.search(r'^\d{1,2}\.\d{1,2}$', value):
                current_year = datetime.now().year
                month, day = value.split('.')
                value = f"{current_year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'4-7'这样的简单日期格式
            elif re.search(r'^\d{1,2}-\d{1,2}$', value):
                current_year = datetime.now().year
                month, day = value.split('-')
                value = f"{current_year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'2025.4.7'这样的完整日期格式
            elif re.search(r'^\d{4}\.\d{1,2}\.\d{1,2}$', value):
                year, month, day = value.split('.')
                value = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'2025-4-7'这样的格式
            elif re.search(r'^\d{4}-\d{1,2}-\d{1,2}$', value):
                year, month, day = value.split('-')
                value = f"{year}-{month.zfill(2)}-{day.zfill(2)}"

            conditions.append(f"DATE_FORMAT({field}, '%Y-%m-%d') = %s")
            params.append(value)
        else:
            conditions.append(f"{field} LIKE %s")
            params.append(f"%{value}%")
    
    query = """
        SELECT 
            m.id, m.report_code, m.supplier, m.material_number, m.material_name, 
            DATE_FORMAT(m.inspection_date, '%Y-%m-%d') as inspection_date, 
            DATE_FORMAT(m.created_at, '%Y-%m-%d') as created_at, 
            m.inspector, m.final_judgment, m.opinion,
            (SELECT JSON_ARRAYAGG(JSON_OBJECT('question_number', q.question_number, 'question_text', q.question_text))
             FROM material_sample_questions q 
             WHERE q.form_id = m.id) AS questions
        FROM material_sample_confirmation_form m
    """
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    cursor.execute(query, tuple(params))
    results = cursor.fetchall()
    
    # 为每个结果获取问题点并按序号排序
    for result in results:
        cursor.execute("""
            SELECT question_number, question_text 
            FROM material_sample_questions 
            WHERE form_id = %s 
            ORDER BY question_number
        """, (result['id'],))
        questions = cursor.fetchall()
        result['questions'] = questions
    
    # 确保返回的是数组格式
    if not isinstance(results, list):
        results = [results]
    
    cursor.close()
    conn.close()
    return jsonify({'data': results})

@material_confirmation_bp.route('/new')
def new_confirmation():
    """新增物料确认页面"""
    return render_template('material_confirmation/new_confirmation.html')

@material_confirmation_bp.route('/view/<report_code>')
def view_confirmation(report_code):
    """查看物料确认详情"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # 获取基本信息
        cursor.execute("""
            SELECT * FROM material_sample_confirmation_form
            WHERE report_code = %s
        """, (report_code,))

        confirmation = cursor.fetchone()
        if not confirmation:
            return "记录不存在", 404

        # 获取问题点
        cursor.execute("""
            SELECT question_number, question_text, image_path
            FROM material_sample_questions
            WHERE form_id = %s
            ORDER BY question_number
        """, (confirmation['id'],))

        questions = cursor.fetchall()
        confirmation['questions'] = questions

        return render_template('material_confirmation/view_confirmation.html', confirmation=confirmation)

    except Exception as e:
        return f"查看失败: {str(e)}", 500
    finally:
        cursor.close()
        conn.close()

@material_confirmation_bp.route('/edit/<report_code>')
def edit_confirmation(report_code):
    """编辑物料确认页面"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # 获取基本信息
        cursor.execute("""
            SELECT * FROM material_sample_confirmation_form
            WHERE report_code = %s
        """, (report_code,))

        confirmation = cursor.fetchone()
        if not confirmation:
            return "记录不存在", 404

        # 获取问题点
        cursor.execute("""
            SELECT question_number, question_text, image_path
            FROM material_sample_questions
            WHERE form_id = %s
            ORDER BY question_number
        """, (confirmation['id'],))

        questions = cursor.fetchall()
        confirmation['questions'] = questions

        return render_template('material_confirmation/edit_confirmation.html', confirmation=confirmation)

    except Exception as e:
        return f"编辑失败: {str(e)}", 500
    finally:
        cursor.close()
        conn.close()
