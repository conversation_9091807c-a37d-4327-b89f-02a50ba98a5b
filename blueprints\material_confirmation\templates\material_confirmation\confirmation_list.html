{% extends "base.html" %}

{% block title %}物料确认清单 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    body {
        width: 100%;
        margin: 0 auto;
        font-family: "Microsoft YaHei";
        font-size: 12px;
        overflow-x: auto;
        overflow-y: auto;
        box-sizing: border-box;
    }
    table {
        table-layout: fixed;
        width: 100%;
        max-width: 1000px;
        border-collapse: collapse;
        border: 2px solid #000000;
    }
    th, td {
        border: 1px solid #000000;
        padding: 4px;
        text-align: center;
    }
    th {
        background-color: #F2F2F2;
        font-weight: bold;
    }
    .search-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin: 10px 0;
    }
    .search-field {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    .action-buttons {
        margin: 10px 0;
        position: relative;
        z-index: 10;
        clear: both;
        display: flex !important;
        justify-content: flex-start !important;
        align-items: center !important;
        gap: 10px !important;
    }
    .btn {
        padding: 5px 10px;
        margin: 0 5px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        position: relative;
        z-index: 11;
        pointer-events: auto;
        display: inline-block;
    }
    .btn-primary { background-color: #007bff; color: white; }
    .btn-info { background-color: #17a2b8; color: white; }
    .btn-warning { background-color: #ffc107; color: black; }
    .btn-danger { background-color: #dc3545; color: white; }
    .btn:hover { opacity: 0.8; }
    .alert {
        padding: 10px 15px;
        margin: 10px 0;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }
    .alert-info {
        color: #0c5460;
        background-color: #d1ecf1;
        border-color: #bee5eb;
    }
    .btn-close {
        float: right;
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
    }

    /* 选中行的样式 */
    #material-table tbody tr.selected {
        background-color: #e3f2fd;
    }

    /* 复选框样式改进 */
    #material-table input[type="checkbox"] {
        transform: scale(1.2);
        margin: 0;
    }

    /* 按钮禁用状态 */
    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* 确保搜索表单不会覆盖按钮 */
    #searchForm {
        position: relative;
        z-index: 1;
        margin-bottom: 15px;
    }

    /* 确保按钮区域清晰可见 */
    .action-buttons {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }

    /* 强制按钮可点击 */
    .action-buttons .btn {
        pointer-events: auto !important;
        position: relative !important;
        z-index: 999 !important;
        display: inline-block !important;
    }

    .action-buttons .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1>物料确认清单</h1>
</div>

<!-- Flash消息显示 -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
            <div class="alert alert-{{ 'warning' if category == 'warning' else 'info' }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}
{% endwith %}

<div style="margin: 10px 0;">
    <form id="searchForm" onsubmit="event.preventDefault(); performSearch();">
    <div id="search-container" class="search-container" style="gap: 0.1px;">
        <div class="search-field base-search">
            <select name="search_field" style="width: 120px;">
                <option value="supplier">供应商</option>
                <option value="material_number">物料料号</option>
                <option value="material_name">物料名称</option>
                <option value="inspection_date">送检日期</option>
                <option value="created_at">确认日期</option>
                <option value="inspector">检验员</option>
                <option value="final_judgment">结果</option>
            </select>
            <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容" 
                   onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
            <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
            <button type="button" class="search-btn" onclick="performSearch();">搜索</button>
        </div>
    </div>
</form>
</div>

<div class="action-buttons" style="margin: 10px 0;">
    <button type="button" class="btn btn-primary" onclick="addMaterialSample(); console.log('新增报告按钮被点击');">新增报告</button>
    <button type="button" class="btn btn-info" onclick="viewReport(); console.log('查看报告详情按钮被点击');">查看报告详情</button>
    <button type="button" class="btn btn-warning" onclick="editSelectedMaterialSample(); console.log('修改按钮被点击');">修改</button>
    <button type="button" class="btn btn-danger" onclick="deleteSelectedMaterialSamples(); console.log('删除按钮被点击');">删除</button>
    <button type="button" class="btn" style="background-color: #6c757d; color: white;" onclick="alert('测试按钮可以点击！'); console.log('测试按钮被点击');">测试按钮</button>
    <span style="margin-left: 20px; color: #666; font-size: 12px;">
        💡 提示：勾选记录后点击"查看报告详情"，或直接双击行来查看详细报告
    </span>
</div>

<table id="material-table">
    <thead>
        <tr>
            <th style="width: 30px;"><input type="checkbox" id="select-all"></th>
            <th style="width: 30px;">序号</th>
            <th style="width: 120px;">报告编码</th>
            <th style="width: 80px;">供应商</th>
            <th style="width: 80px;">物料料号</th>
            <th style="width: 80px;">物料名称</th>
            <th style="width: 80px;">送检日期</th>
            <th style="width: 80px;">确认日期</th>
            <th style="width: 80px;">检验员</th>
            <th style="width: 60px;">结果</th>
            <th style="width: 250px;">意见</th>
            <th style="width: 300px;">问题点</th>
            <th style="width: 120px;">操作</th>
        </tr>
    </thead>
    <tbody>
        {% for item in material_samples %}
        <tr>
            <td><input type="checkbox"></td>
            <td>{{ loop.index }}</td>
            <td>{{ item.report_code }}</td>
            <td>{{ item.supplier }}</td>
            <td>{{ item.material_number }}</td>
            <td>{{ item.material_name }}</td>
            <td>{{ item.inspection_date }}</td>
            <td>{{ item.created_at }}</td>
            <td>{{ item.inspector }}</td>
            <td>{{ item.final_judgment }}</td>
            <td>{{ item.opinion }}</td>
            <td style="text-align: left;">{% for question in item.questions %}
                {{ question.question_number }}. {{ question.question_text }}{% if not loop.last %}<br>{% endif %}
                {% endfor %}
            </td>
            <td>
                <a href="/Material_Sample_Confirmation_Form/load-data/{{ item.report_code }}" class="btn btn-sm btn-info" target="_blank">查看详情</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}

{% block extra_js %}
<script>
function getSelectedMaterialSampleIds() {
    const checkboxes = document.querySelectorAll('#material-table tbody input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(checkbox => {
        const row = checkbox.closest('tr');
        return row.querySelector('td:nth-child(3)').textContent; // 获取报告编码
    });
}

function addMaterialSample() {
    console.log('addMaterialSample clicked');
    window.location.href = "{{ url_for('material_confirmation.new_confirmation') }}";
}

function editSelectedMaterialSample() {
    const selectedIds = getSelectedMaterialSampleIds();

    if (selectedIds.length !== 1) {
        alert('请选择一条记录进行修改');
        return;
    }
    window.open(`{{ url_for('material_confirmation.edit_confirmation', report_code='PLACEHOLDER') }}`.replace('PLACEHOLDER', selectedIds[0]), "_blank");
}

function viewReport() {
    console.log('viewReport clicked');
    const selectedIds = getSelectedMaterialSampleIds();
    console.log('Selected IDs:', selectedIds);

    if (selectedIds.length === 0) {
        alert('请先勾选要查询的报告记录');
        return;
    }

    if (selectedIds.length > 1) {
        alert('一次只能查询一条报告，请只选择一条记录');
        return;
    }

    // 显示加载提示
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = '正在加载...';
    button.disabled = true;

    // 使用load-data功能来查看报告详情
    const reportCode = selectedIds[0].trim();
    const url = `/Material_Sample_Confirmation_Form/load-data/${reportCode}`;

    // 打开新窗口
    const newWindow = window.open(url, "_blank");

    // 检查窗口是否成功打开
    if (newWindow) {
        // 恢复按钮状态
        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
        }, 1000);
    } else {
        alert('无法打开新窗口，请检查浏览器弹窗设置');
        button.textContent = originalText;
        button.disabled = false;
    }
}

function deleteSelectedMaterialSamples() {
    const selectedIds = getSelectedMaterialSampleIds();
    if (selectedIds.length === 0) {
        alert('请选择要删除的记录');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedIds.length} 条记录吗？`)) {
        fetch('{{ url_for("material_confirmation.delete_confirmation") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ report_codes: selectedIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('删除成功');
                location.reload();
            } else {
                alert(data.error || '删除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败');
        });
    }
}

document.getElementById('select-all').addEventListener('click', function() {
    const checkboxes = document.querySelectorAll('#material-table tbody input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
        // 更新行的选中状态
        const row = checkbox.closest('tr');
        if (checkbox.checked) {
            row.classList.add('selected');
        } else {
            row.classList.remove('selected');
        }
    });
});

// 为每个复选框添加事件监听器
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('#material-table tbody input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const row = this.closest('tr');
            if (this.checked) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }

            // 更新全选复选框状态
            const allCheckboxes = document.querySelectorAll('#material-table tbody input[type="checkbox"]');
            const checkedCheckboxes = document.querySelectorAll('#material-table tbody input[type="checkbox"]:checked');
            const selectAllCheckbox = document.getElementById('select-all');

            if (checkedCheckboxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedCheckboxes.length === allCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        });
    });

    // 添加按钮事件监听器（备用方案）
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn')) {
            console.log('按钮被点击:', e.target.textContent);
        }
    });

    // 为表格行添加双击事件
    const tableRows = document.querySelectorAll('#material-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('dblclick', function() {
            const reportCode = this.querySelector('td:nth-child(3)').textContent.trim();
            if (reportCode) {
                window.open(`/Material_Sample_Confirmation_Form/load-data/${reportCode}`, "_blank");
            }
        });

        // 添加鼠标悬停效果
        row.addEventListener('mouseenter', function() {
            if (!this.classList.contains('selected')) {
                this.style.backgroundColor = '#f5f5f5';
            }
        });

        row.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.backgroundColor = '';
            }
        });
    });
});

// 处理alert关闭
document.querySelectorAll('.btn-close').forEach(button => {
    button.addEventListener('click', function() {
        this.closest('.alert').style.display = 'none';
    });
});

// 动态更新搜索框placeholder
document.querySelector('select[name="search_field"]').addEventListener('change', function() {
    const searchValueInput = document.querySelector('input[name="search_value"]');
    if (this.value === 'inspection_date' || this.value === 'created_at') {
      searchValueInput.placeholder = '格式: 3-8 或 2025-3-8';
    } else {
      searchValueInput.placeholder = '请输入搜索内容';
    }
});

function addSearchField() {
    const container = document.getElementById('search-container');
    const searchFields = container.querySelectorAll('.search-field');
    if (searchFields.length >= 5) {
        alert('最多只能添加5个搜索条件');
        return;
    }
    const newField = document.createElement('div');
    newField.className = 'search-field';
    newField.innerHTML = `
      <select name="search_field" style="width: 120px;">
        <option value="supplier">供应商</option>
        <option value="material_number">物料料号</option>
        <option value="material_name">物料名称</option>
        <option value="inspection_date">送检日期</option>
        <option value="created_at">确认日期</option>
        <option value="inspector">检验员</option>
        <option value="final_judgment">结果</option>
      </select>
      <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容..." 
             onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
      <button type="button" onclick="this.parentNode.remove()">×</button>
    `;
    newField.querySelector('select').addEventListener('change', function() {
      const searchValueInput = this.parentNode.querySelector('input[name="search_value"]');
      if (this.value === 'inspection_date' || this.value === 'created_at') {
        searchValueInput.placeholder = '格式: 3-8 或 2025-3-8';
      } else {
        searchValueInput.placeholder = '请输入搜索内容...';
      }
    });
    container.appendChild(newField);
}

function performSearch() {
    const searchFields = document.querySelectorAll('.search-field');
    const searchParams = new URLSearchParams();
    
    searchFields.forEach(field => {
        const fieldSelect = field.querySelector('select[name="search_field"]');
        const fieldInput = field.querySelector('input[name="search_value"]');
        
        if (fieldSelect && fieldInput && fieldInput.value) {
            searchParams.append('search_field', fieldSelect.value);
            searchParams.append('search_value', fieldInput.value);
        }
    });
    
    if (searchParams.toString() === '') {
        alert('请输入搜索内容');
        return;
    }

    fetch(`{{ url_for('material_confirmation.search_material_samples') }}?${searchParams.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                alert(data.error);
                return;
            }
            function updateTable(data) {
                if (!Array.isArray(data)) {
                    data = [data];
                }
            const tbody = document.querySelector('#material-table tbody');
            tbody.innerHTML = data.map((item, index) => `
                <tr>
                    <td><input type="checkbox"></td>
                    <td>${index + 1}</td>
                    <td>${item.report_code || ''}</td>
                    <td>${item.supplier || ''}</td>
                    <td>${item.material_number || ''}</td>
                    <td>${item.material_name || ''}</td>
                    <td>${item.inspection_date || ''}</td>
                    <td>${item.created_at || ''}</td>
                    <td>${item.inspector || ''}</td>
                    <td>${item.final_judgment || ''}</td>
                    <td>${item.opinion || ''}</td>
                    <td style="text-align: left;">
                        ${item.questions ? item.questions.map(q => `${q.question_number}. ${q.question_text}`).join('<br>') : ''}
                    </td>
                </tr>`
            ).join('')
        }
        updateTable(data.data);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('搜索失败: ' + error.message);
        });
}
</script>
{% endblock %}
