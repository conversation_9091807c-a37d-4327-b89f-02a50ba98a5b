{% extends "base.html" %}

{% block title %}物料确认清单 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    body {
        width: 100%;
        margin: 0 auto;
        font-family: "Microsoft YaHei";
        font-size: 12px;
        overflow-x: auto;
        overflow-y: auto;
        box-sizing: border-box;
    }
    table {
        table-layout: fixed;
        width: 100%;
        max-width: 1000px;
        border-collapse: collapse;
        border: 2px solid #000000;
    }
    th, td {
        border: 1px solid #000000;
        padding: 4px;
        text-align: center;
    }
    th {
        background-color: #F2F2F2;
        font-weight: bold;
    }
    .search-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin: 10px 0;
    }
    .search-field {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    .action-buttons {
        margin: 10px 0;
    }
    .btn {
        padding: 5px 10px;
        margin: 0 5px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
    }
    .btn-primary { background-color: #007bff; color: white; }
    .btn-info { background-color: #17a2b8; color: white; }
    .btn-warning { background-color: #ffc107; color: black; }
    .btn-danger { background-color: #dc3545; color: white; }
    .btn:hover { opacity: 0.8; }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1>物料确认清单</h1>
</div>

<div style="margin: 10px 0;">
    <form id="searchForm" onsubmit="event.preventDefault(); performSearch();">
    <div id="search-container" class="search-container" style="gap: 0.1px;">
        <div class="search-field base-search">
            <select name="search_field" style="width: 120px;">
                <option value="supplier">供应商</option>
                <option value="material_number">物料料号</option>
                <option value="material_name">物料名称</option>
                <option value="inspection_date">送检日期</option>
                <option value="created_at">确认日期</option>
                <option value="inspector">检验员</option>
                <option value="final_judgment">结果</option>
            </select>
            <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容" 
                   onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
            <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
            <button type="button" class="search-btn" onclick="performSearch();">搜索</button>
        </div>
    </div>
</form>
</div>

<div class="action-buttons" style="margin: 10px 0;">
    <button type="button" class="btn btn-primary" onclick="addMaterialSample()">新增报告</button>        
    <button type="button" class="btn btn-info" onclick="viewReport()">查询报告</button> 
    <button type="button" class="btn btn-warning" onclick="editSelectedMaterialSample()">修改</button>
    <button type="button" class="btn btn-danger" onclick="deleteSelectedMaterialSamples()">删除</button>
</div>

<table id="material-table">
    <thead>
        <tr>
            <th style="width: 30px;"><input type="checkbox" id="select-all"></th>
            <th style="width: 30px;">序号</th>
            <th style="width: 120px;">报告编码</th>
            <th style="width: 80px;">供应商</th>
            <th style="width: 80px;">物料料号</th>
            <th style="width: 80px;">物料名称</th>
            <th style="width: 80px;">送检日期</th>
            <th style="width: 80px;">确认日期</th>
            <th style="width: 80px;">检验员</th>
            <th style="width: 60px;">结果</th>
            <th style="width: 250px;">意见</th>
            <th style="width: 300px;">问题点</th>
        </tr>
    </thead>
    <tbody>
        {% for item in material_samples %}
        <tr>
            <td><input type="checkbox"></td>
            <td>{{ loop.index }}</td>
            <td>{{ item.report_code }}</td>
            <td>{{ item.supplier }}</td>
            <td>{{ item.material_number }}</td>
            <td>{{ item.material_name }}</td>
            <td>{{ item.inspection_date }}</td>
            <td>{{ item.created_at }}</td>
            <td>{{ item.inspector }}</td>
            <td>{{ item.final_judgment }}</td>
            <td>{{ item.opinion }}</td>
            <td style="text-align: left;">{% for question in item.questions %}
                {{ question.question_number }}. {{ question.question_text }}{% if not loop.last %}<br>{% endif %}
                {% endfor %}
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}

{% block extra_js %}
<script>
function getSelectedMaterialSampleIds() {
    const checkboxes = document.querySelectorAll('#material-table tbody input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(checkbox => {
        const row = checkbox.closest('tr');
        return row.querySelector('td:nth-child(3)').textContent; // 获取报告编码
    });
}

function addMaterialSample() {
    // 这里需要根据实际的物料确认表单路由进行调整
    window.open("{{ url_for('Material_Sample_Confirmation_Form.material_sample_confirmation_form') }}", "_blank");
}

function editSelectedMaterialSample() {
    const selectedIds = getSelectedMaterialSampleIds();
    
    if (selectedIds.length !== 1) {
        alert('请选择一条记录进行修改');
        return;
    }
    // 这里需要根据实际的修改路由进行调整
    window.open(`/Material_Sample_Confirmation_Form_modify/${selectedIds[0]}`, "_blank");
}

function viewReport() {
    const selectedIds = getSelectedMaterialSampleIds();
    
    if (selectedIds.length !== 1) {
        alert('请选择一条记录进行查询');
        return;
    }
    // 这里需要根据实际的查看路由进行调整
    window.open(`/Material_Sample_Confirmation_Form/load-data/${selectedIds[0]}`, "_blank");
}

function deleteSelectedMaterialSamples() {
    const selectedIds = getSelectedMaterialSampleIds();
    if (selectedIds.length === 0) {
        alert('请选择要删除的记录');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedIds.length} 条记录吗？`)) {
        fetch('{{ url_for("material_confirmation.delete_confirmation") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ report_codes: selectedIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('删除成功');
                location.reload();
            } else {
                alert(data.error || '删除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败');
        });
    }
}

document.getElementById('select-all').addEventListener('click', function() {
    const checkboxes = document.querySelectorAll('#material-table tbody input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
});

// 动态更新搜索框placeholder
document.querySelector('select[name="search_field"]').addEventListener('change', function() {
    const searchValueInput = document.querySelector('input[name="search_value"]');
    if (this.value === 'inspection_date' || this.value === 'created_at') {
      searchValueInput.placeholder = '格式: 3-8 或 2025-3-8';
    } else {
      searchValueInput.placeholder = '请输入搜索内容';
    }
});

function addSearchField() {
    const container = document.getElementById('search-container');
    const searchFields = container.querySelectorAll('.search-field');
    if (searchFields.length >= 5) {
        alert('最多只能添加5个搜索条件');
        return;
    }
    const newField = document.createElement('div');
    newField.className = 'search-field';
    newField.innerHTML = `
      <select name="search_field" style="width: 120px;">
        <option value="supplier">供应商</option>
        <option value="material_number">物料料号</option>
        <option value="material_name">物料名称</option>
        <option value="inspection_date">送检日期</option>
        <option value="created_at">确认日期</option>
        <option value="inspector">检验员</option>
        <option value="final_judgment">结果</option>
      </select>
      <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容..." 
             onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
      <button type="button" onclick="this.parentNode.remove()">×</button>
    `;
    newField.querySelector('select').addEventListener('change', function() {
      const searchValueInput = this.parentNode.querySelector('input[name="search_value"]');
      if (this.value === 'inspection_date' || this.value === 'created_at') {
        searchValueInput.placeholder = '格式: 3-8 或 2025-3-8';
      } else {
        searchValueInput.placeholder = '请输入搜索内容...';
      }
    });
    container.appendChild(newField);
}

function performSearch() {
    const searchFields = document.querySelectorAll('.search-field');
    const searchParams = new URLSearchParams();
    
    searchFields.forEach(field => {
        const fieldSelect = field.querySelector('select[name="search_field"]');
        const fieldInput = field.querySelector('input[name="search_value"]');
        
        if (fieldSelect && fieldInput && fieldInput.value) {
            searchParams.append('search_field', fieldSelect.value);
            searchParams.append('search_value', fieldInput.value);
        }
    });
    
    if (searchParams.toString() === '') {
        alert('请输入搜索内容');
        return;
    }

    fetch(`{{ url_for('material_confirmation.search_material_samples') }}?${searchParams.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                alert(data.error);
                return;
            }
            function updateTable(data) {
                if (!Array.isArray(data)) {
                    data = [data];
                }
            const tbody = document.querySelector('#material-table tbody');
            tbody.innerHTML = data.map((item, index) => `
                <tr>
                    <td><input type="checkbox"></td>
                    <td>${index + 1}</td>
                    <td>${item.report_code || ''}</td>
                    <td>${item.supplier || ''}</td>
                    <td>${item.material_number || ''}</td>
                    <td>${item.material_name || ''}</td>
                    <td>${item.inspection_date || ''}</td>
                    <td>${item.created_at || ''}</td>
                    <td>${item.inspector || ''}</td>
                    <td>${item.final_judgment || ''}</td>
                    <td>${item.opinion || ''}</td>
                    <td style="text-align: left;">
                        ${item.questions ? item.questions.map(q => `${q.question_number}. ${q.question_text}`).join('<br>') : ''}
                    </td>
                </tr>`
            ).join('')
        }
        updateTable(data.data);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('搜索失败: ' + error.message);
        });
}
</script>
{% endblock %}
