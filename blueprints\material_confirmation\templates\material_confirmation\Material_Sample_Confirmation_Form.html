<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>物料样板确认书</title>
    <style>
        .image-upload-cell 
        .image-upload-box{
            height: 120px;
            position: relative;
        }
        body {
            width: 1000px;  /* 根据实际需求调整 */
            margin: 0 auto;
            font-family: "Microsoft YaHei"; 
            font-size: 12px;
        }
        table {
            table-layout: fixed;  /* 固定表格布局 */
            width: 100%;
            max-width: 1000px;
            border-collapse: collapse;
            border: 2px solid #000000;
            border-spacing: 0; /* 显式声明 */
        }
        td input[type="text"] {
            width: 100%;
            box-sizing: border-box;
            padding: 4px 3px;
            margin: 0;
            display: block;
            height: 100%;
            border: none;
        }
        td input[type="text"]:focus {
            outline: 2px solid #000000;
            border: 2px solid #000000;
            border-radius: 5px;
        }
        td input[type="text"] {
            height: 100%;
            box-sizing: border-box;
            padding: 4px 3px;
            margin: 0;
            display: block;
        }
        td input[type="text"]:focus {
            outline: 2px solid #000000;
            border-radius: 5px;
        }
        td {
            border: 1px solid #000000;
            padding: 2px 1px;
            vertical-align: middle;
            line-height: 1.2;
        }
        .main-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            background: #D9D9D9;
            padding: 8px 0;
        }
        .module-title {
            background: #F2F2F2;
            font-weight: bold;
            padding-left: 4px;
        }
        .sub-header {
            background: #E6E6E6;
        }
        .col-no { width: 35px; }
        .col-pos { width: 90px; }
        .col-value { width: 55px; }
        .col-measure { width: 35px; }
        .col-check { width: 70px; }
        .col-note { width: 100px; }
        .align-center { text-align: center; }
        .align-left { text-align: left; padding-left: 4px; }
        .check-box {
            width: 14px;
            height: 14px;
            border: 1px solid #000;
            display: inline-block;
            margin: 0 5px;
            vertical-align: middle;
        }
        input[type="checkbox"] {
            margin: 0 3px 0 10px;
        }

        .custom-row-height {
            width: 100%; 
            border-collapse: collapse;
            table-layout: fixed;
            border: none;
            border-spacing: 0; /* 显式声明 */
            height: 10px; /* 根据需要调整高度 */
        }

        .custom-row-height td {
            height: 10px; /* 确保单元格的高度与行高一致 */
            vertical-align: middle;
        }
        textarea {
            /* border: none !important; /* 移除边框 
            resize: none !important;; 
            overflow: auto !important;;
            outline: none !important;;*/
            width: 100%; /* 输入框宽度与列宽一致 */
            height: auto; /* 自动调整高度 */
            min-height: 50px; /* 最小高度 */
            max-height: 200px; /* 最大高度 */
            padding: 4px 3px; /* 与td padding保持一致 */
            margin: 0;
            font-size: 13px;
            box-sizing: border-box !important;; /* 包含内边距和边框 */
            border: 1px solid transparent!important;; /* 默认无边框 */
            resize: vertical!important;; /* 允许垂直调整大小 */
            overflow-y: auto!important;; /* 允许垂直滚动 */
            outline: none!important;; /* 移除焦点时的轮廓 */
        }
        textarea:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        textarea[name^="appearance_"][name$="_note"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important;
        }

        textarea[name^="appearance_"][name$="_note"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }       

        textarea[name^="function_"][name$="_note"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important;
        }

        textarea[name^="function_"][name$="_note"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        textarea[name^="function_"][name$="_other"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 58px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important; /* 移除边框 */;
        }

        textarea[name^="function_"][name$="_other"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        input[name="review"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important; /* 移除边框 */
            border-radius: 5px;
        }
        .checkbox-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .col-compliance {
            width: 100px;  /* 根据需求调整具体数值 */
            text-align: center;
        }
        label {
            margin-left: 1px; /* 文字与复选框的间距 */
            cursor: pointer; /* 鼠标悬停时显示手型 */
            user-select: none; /* 禁止文字被选中 */
        }
        /* 图片上传样式 */
        .image-upload-box {
            position: relative;
            width: 100%;
            height: 80px;
            border: 2px dashed #ccc;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .image-upload-box:hover {
            border-color: #999;
            background-color: rgba(0, 0, 0, 0.02);
        }

        .image-upload-box.drag-over {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .image-upload-box.active-hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.05);
        }

        .delete-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            display: none;
            z-index: 2;
            opacity: 0.5; /* 默认半透明 */
            transition: opacity 0.2s ease-in-out;
        }

        .preview-image {
            width: 100%;
            height: 100%; /* 根据图片比例自动调整 */
            object-fit: contain; /* 保持图片比例 */
            position: absolute;
            top: 0;
            left: 0;
        }

        .upload-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            white-space: nowrap;
            font-size: 12px;
            text-align: center;
            width: 90%;
        }

        .upload-shortcut {
            display: block;
            font-size: 10px;
            color: #999;
            margin-top: 4px;
        }

        .image-upload-box.uploading .upload-overlay {
            content: '上传中...';
        }

        .image-note {
            width: 100%;
            height: 20px;
            margin-top: 2px;
            font-size: 12px;
        }

        input[type="file"] {
            display: none;
        }
        .image-upload-box.uploading::after {
            content: "处理中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-size: 14px;
            z-index: 6;
        }
        /* 可选：保持复选框样式 */
        input[name="final_judgment"] {
            display: none;
        }
        input[name="final_judgment"] + label {
            padding: 4px 8px;
            border: 1px solid #999;
            border-radius: 4px;
            cursor: pointer;
        }
        input[name="final_judgment"]:checked + label {
            background-color: #007bff;
            color: white;
        }
 
        .image-upload-box.has-image .delete-btn {
            display: block; /* 有图片时始终显示按钮，但半透明 */
        }

        .image-upload-box.has-image:hover .delete-btn {
            opacity: 1; /* 鼠标悬停时完全不透明 */
        }

        .delete-btn:hover {
            opacity: 1; /* 鼠标悬停在按钮上时完全不透明 */
        }

        .feedback-message {
            position: fixed;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 10px 15px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 4px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            text-align: center;
            min-width: 200px;
            max-width: 80%;
        }

        .feedback-message.show {
            opacity: 1;
        }

        /* 添加图片压缩功能 */
        .image-upload-box.uploading::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 5;
        }

        .image-upload-box.uploading::after {
            content: "处理中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-size: 14px;
            z-index: 6;
        }

        .paste-button {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background: rgba(0, 123, 255, 0.85);
            color: white;
            border: none;
            border-radius: 3px;
            padding: 4px;
            padding-left: 4px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            z-index: 20;
            opacity: 0.9;
            display: none;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            width: 28px; /* 初始状态只显示图标的宽度 */
            text-align: left; /* 改为左对齐，确保文字显示 */
            overflow: visible; /* 修改为可见，不裁剪溢出内容 */
            white-space: nowrap; /* 防止文字换行 */
        }

        .image-upload-box:hover .paste-button {
            display: block;
        }

        .image-upload-box.has-image .paste-button {
            bottom: 5px; /* 保持在底部位置 */
            display: block; /* 修改为显示 */
        }

        .paste-button:hover {
            opacity: 1;
            background: rgba(0, 123, 255, 1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            width: 70px; /* 悬停按钮本身时展开 */
            z-index: 30; /* 确保悬停时按钮在最上层 */
            padding-left: 8px; /* 确保文字有足够的左边距 */
            font-weight: bold;
            color: white;
            overflow: visible;
            height: auto;
            min-height: 24px;
        }
        
        /* 添加复制按钮样式 */
        .copy-button {
            position: absolute;
            bottom: 33px; /* 位于粘贴按钮上方 */
            right: 5px;
            background: rgba(255, 165, 0, 0.85); /* 橙色背景 */
            color: white;
            border: none;
            border-radius: 3px;
            padding: 4px;
            padding-left: 4px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            z-index: 20;
            opacity: 0.9;
            display: none;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            width: 28px; /* 初始状态只显示图标的宽度 */
            text-align: left; /* 改为左对齐 */
            overflow: visible; /* 修改为可见 */
            white-space: nowrap; /* 防止文字换行 */
        }
        
        /* 添加上传按钮样式 */
        .upload-button {
            position: absolute;
            bottom: 61px; /* 增加与复制按钮的间隙 */
            right: 5px;
            background: rgba(76, 175, 80, 0.85);
            color: white;
            border: none;
            border-radius: 3px;
            padding: 4px;
            padding-left: 4px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            z-index: 20;
            opacity: 0.9;
            display: none;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            width: 28px; /* 初始状态只显示图标的宽度 */
            text-align: left; /* 改为左对齐 */
            overflow: visible; /* 修改为可见 */
            white-space: nowrap; /* 防止文字换行 */
        }
        
        .image-upload-box:hover .paste-button,
        .image-upload-box:hover .copy-button,
        .image-upload-box:hover .upload-button {
            display: block;
            opacity: 1;
        }
        
        .image-upload-box.has-image .paste-button {
            bottom: 5px; /* 保持在底部位置 */
            display: block; /* 修改为显示 */
        }
        
        .image-upload-box.has-image .copy-button {
            bottom: 33px; /* 与粘贴按钮保持一致的间隙 */
            display: block; /* 修改为显示 */
        }
        
        .image-upload-box.has-image .upload-button {
            bottom: 61px; /* 与复制按钮保持一致的间隙 */
            display: block; /* 修改为显示 */
        }
        
        .copy-button:hover {
            opacity: 1;
            background: rgba(255, 165, 0, 1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            width: 70px; /* 悬停按钮本身时展开 */
            z-index: 30; /* 确保悬停时按钮在最上层 */
            padding-left: 8px; /* 确保文字有足够的左边距 */
            font-weight: bold;
            color: white;
            overflow: visible;
            height: auto;
            min-height: 24px;
        }
        
        .upload-button:hover {
            opacity: 1;
            background: rgba(76, 175, 80, 1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            width: 70px; /* 悬停按钮本身时展开 */
            z-index: 30; /* 确保悬停时按钮在最上层 */
            padding-left: 8px; /* 确保文字有足够的左边距 */
            font-weight: bold;
            color: white;
            overflow: visible;
            height: auto;
            min-height: 24px;
        }

        /* 添加新规则：让所有按钮始终显示 */
        .paste-button, .copy-button, .upload-button {
            display: block !important;
            opacity: 0.9;
        }
    </style>
    
    <!-- 添加图片放大模态框 -->
    <style>
        /* 图片放大模态框样式 */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            cursor: zoom-in; /* 初始光标 */
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .image-modal.show {
            opacity: 1;
        }
        .image-modal.grabbing {
            cursor: grabbing !important;
        }

        /* 图片容器 */
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            max-width: 90%;
            max-height: 90%;
            border: 2px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, filter 0.3s ease;
            filter: blur(2px);
        }
        .modal-content.show {
            transform: translate(-50%, -50%) scale(1);
            filter: blur(0);
        }
        .modal-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s linear infinite;
            z-index: 1001;
            display: none;
        }
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
            z-index: 1002;
        }
        .close:hover {
            opacity: 1;
        }
        .image-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 20px;
            padding: 5px 15px;
            display: flex;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .image-modal:hover .image-controls {
            opacity: 0.7;
        }
        .image-controls:hover {
            opacity: 1 !important;
        }
        .control-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            margin: 0 10px;
            cursor: pointer;
            padding: 5px 10px;
        }
        .control-btn:hover {
            background-color: rgba(255,255,255,0.2);
            border-radius: 4px;
        }
        .tooltip {
            position: absolute;
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <form id="material-form">
        <table>
            <!-- 主标题 -->
            <tr>
                <a href="{{ url_for('index') }}">返回确认清单</a>
                <td colspan="14" class="main-title">物料样板确认书
                    <button type="button" id="submit-btn" style="float: right; margin-right: 20px;">提交</button>
                </td>
                
            </tr>
            <!-- 报告编码 -->
            <tr>
                <td colspan="14">报告编码: <span id="report-code">表单提交后自动生成...</span></td>
            </tr>   


            <!-- 供应商信息 -->
            <tr>
                <td colspan="3" class="align-left">供应商/车间<br>（代理商要注明原厂）</td>
                <td colspan="3" >
                    <input type="text" name="supplier" id="supplier" required>
                </td>
                <td colspan="2" class="align-center">送检日期</td>
                <td colspan="2">
                    <input 
                        type="date" 
                        name="inspection_date" 
                        id="date"
                        value=""
                        style="width: 100%; box-sizing: border-box;"
                    >
                </td>
                <td>样板数量</td>
                <td>
                    <input type="text" 
                    name="sample_count"
                    id="sample_count" >
                </td>
                <td>检验员</td>
                <td>
                    <input type="text"
                    name="inspector"
                    id="inspector" >
                </td>
            </tr>

            <!-- 物料信息行 - 进一步调整结构以完全匹配图片 -->
            <tr>
                <td colspan="3">物料料号</td>          
                <td colspan="3">图号</td>
                <td colspan="3">物料名称</td>
                <td colspan="1">图纸版本</td>
                <td colspan="2"> 材质</td>
                <td colspan="2">表面处理</td>
            </tr>

            <!-- 添加第四行输入框 -->
            <tr>
                <td colspan="3" style="height: 20px;">
                    <input type="text" name="material_number" id="material-number" required>
                </td>
                <td colspan="3" style="height: 20px;">
                    <input type="text" name="graph_number" id="graph-number">
                </td>
                <td colspan="3" style="height: 20px;">
                    <input type="text" name="material_name" id="material-name">
                </td>
                <td colspan="1" style="height: 20px;">
                    <input type="text" name="drawing_version" id="drawing-version">
                </td>
                <td colspan="2" style="height: 20px;">
                    <input type="text" name="material_texture" id="material-texture">
                </td>
                <td colspan="2" style="height: 20px;">
                    <input type="text" name="surface_processing" id="surface-processing">
                </td>
            </tr>

            <!-- 样板状态 -->
            <tr>
                <td colspan="14" class="align-left" style="height: 25px;">
                    样板提供状态：
                    <input type="checkbox" id="trial-sample"> <label for="trial-sample">试产样品</label>
                    <input type="checkbox" id="big-sample" checked> <label for="big-sample">大货样品</label>
                    <input type="checkbox" id="template"> <label for="template">重制样板</label>
                    <input type="checkbox" id="other"> <label for="other">其他：</label>
                    <input type="text" id="other_textbox" name="other_textbox" style="display: none ;width: 300px;display: inline-block;"; white-space: nowrap;" placeholder="请输入其他内容">
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            document.getElementById('other_textbox').style.display = 'none';
                            document.getElementById('other').addEventListener('change', function() {
                                document.getElementById('other_textbox').style.display = this.checked ? 'inline-block' : 'none';
                            });
                        });
                    </script>
                </td>
            </tr>

            <!-- ================= 尺寸检查 ================= -->
            <tr class="module-title">
                <td id="size-section-cell" rowspan="5" style="width: 30px;">一、尺寸</td>
                <td colspan="5" class="align-center">图纸尺寸</td>
                <td colspan="5" class="align-center">实测尺寸(mm)</td>
                <td colspan="1" class="col-compliance-size align-center" rowspan="2">符合性</td>
                <td colspan="2" class="align-center" rowspan="2">注意事项</td>
            </tr>
            <tr class="sub-header">
                <td class="col-no align-center">NO</td>
                <td class="col-pos">位置</td>
                <td class="col-value">标准值</td>
                <td class="col-value">最小值</td>
                <td class="col-value">最大值</td>
                <td class="col-measure">1</td>
                <td class="col-measure">2</td>
                <td class="col-measure">3</td>
                <td class="col-measure">4</td>
                <td class="col-measure">5</td>
            </tr>
            <!-- 尺寸数据行 -->
            <tr class="custom-row-height">
                <td class="align-center">1</td>
                <td data-id="size-1" data-image="true"><input type="text" name="size_1_position" id="size-1"></td>
                <td><input type="text" name="size_1_value" id="size-1-value"></td>
                <td><input type="text" name="size_1_min" id="size-1-min"></td>
                <td><input type="text" name="size_1_max" id="size-1-max"></td>
                <td><input type="text" name="size_1_measure_1" id="size-1-measure-1"></td>
                <td><input type="text" name="size_1_measure_2" id="size-1-measure-2"></td>
                <td><input type="text" name="size_1_measure_3" id="size-1-measure-3"></td>
                <td><input type="text" name="size_1_measure_4" id="size-1-measure-4"></td>
                <td><input type="text" name="size_1_measure_5" id="size-1-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_1_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_1_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_1_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_1_note" id="size-1-note"></textarea></td>
            </tr>
            
            <tr class="custom-row-height">
                <td class="align-center">2</td>
                <td data-id="size-2" data-image="true"><input type="text" name="size_2_position" id="size-2"></td>
                <td><input type="text" name="size_2_value" id="size-2-value"></td>
                <td><input type="text" name="size_2_min" id="size-2-min"></td>
                <td><input type="text" name="size_2_max" id="size-2-max"></td>
                <td><input type="text" name="size_2_measure_1" id="size-2-measure-1"></td>
                <td><input type="text" name="size_2_measure_2" id="size-2-measure-2"></td>
                <td><input type="text" name="size_2_measure_3" id="size-2-measure-3"></td>
                <td><input type="text" name="size_2_measure_4" id="size-2-measure-4"></td>
                <td><input type="text" name="size_2_measure_5" id="size-2-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_2_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_2_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_2_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_2_note" id="size-2-note"></textarea></td>
            </tr>

            <tr class="custom-row-height">
                <td class="align-center">3</td>
                <td data-id="size-3" data-image="true"><input type="text" name="size_3_position" id="size-3"></td>
                <td><input type="text" name="size_3_value" id="size-3-value"></td>
                <td><input type="text" name="size_3_min" id="size-3-min"></td>
                <td><input type="text" name="size_3_max" id="size-3-max"></td>
                <td><input type="text" name="size_3_measure_1" id="size-3-measure-1"></td>
                <td><input type="text" name="size_3_measure_2" id="size-3-measure-2"></td>
                <td><input type="text" name="size_3_measure_3" id="size-3-measure-3"></td>
                <td><input type="text" name="size_3_measure_4" id="size-3-measure-4"></td>
                <td><input type="text" name="size_3_measure_5" id="size-3-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_3_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_3_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_3_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_3_note" id="size-3-note"></textarea></td>
            </tr>

            <!-- 尺寸行控制按钮 -->
            <tr id="size-row-controls">
                <td colspan="13" style="text-align: center; border: none; padding: 5px;">
                    <button type="button" id="add-size-row" style="margin-right: 10px; padding: 4px 10px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">增加尺寸行</button>
                    <button type="button" id="remove-size-row" style="padding: 4px 10px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">删除尺寸行</button>
                </td>
            </tr>

            <!-- 动态尺寸行（默认隐藏第4-8行） -->
            <tr class="custom-row-height dynamic-size-row" id="size-row-4" style="display: none;">
                <td class="align-center">4</td>
                <td data-id="size-4" data-image="true"><input type="text" name="size_4_position" id="size-4"></td>
                <td><input type="text" name="size_4_value" id="size-4-value"></td>
                <td><input type="text" name="size_4_min" id="size-4-min"></td>
                <td><input type="text" name="size_4_max" id="size-4-max"></td>
                <td><input type="text" name="size_4_measure_1" id="size-4-measure-1"></td>
                <td><input type="text" name="size_4_measure_2" id="size-4-measure-2"></td>
                <td><input type="text" name="size_4_measure_3" id="size-4-measure-3"></td>
                <td><input type="text" name="size_4_measure_4" id="size-4-measure-4"></td>
                <td><input type="text" name="size_4_measure_5" id="size-4-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_4_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_4_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_4_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_4_note" id="size-4-note"></textarea></td>
            </tr>
            <!-- 添加第5-30行的尺寸数据输入行 -->
            <tr class="custom-row-height dynamic-size-row" id="size-row-5" style="display: none;">
                <td class="align-center">5</td>
                <td data-id="size-5" data-image="true"><input type="text" name="size_5_position" id="size-5"></td>
                <td><input type="text" name="size_5_value" id="size-5-value"></td>
                <td><input type="text" name="size_5_min" id="size-5-min"></td>
                <td><input type="text" name="size_5_max" id="size-5-max"></td>
                <td><input type="text" name="size_5_measure_1" id="size-5-measure-1"></td>
                <td><input type="text" name="size_5_measure_2" id="size-5-measure-2"></td>
                <td><input type="text" name="size_5_measure_3" id="size-5-measure-3"></td>
                <td><input type="text" name="size_5_measure_4" id="size-5-measure-4"></td>
                <td><input type="text" name="size_5_measure_5" id="size-5-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_5_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_5_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_5_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_5_note" id="size-5-note"></textarea></td>
            </tr>
            <!-- 添加第6-30行的尺寸数据输入行 -->
            <tr class="custom-row-height dynamic-size-row" id="size-row-6" style="display: none;">
                <td class="align-center">6</td>
                <td data-id="size-6" data-image="true"><input type="text" name="size_6_position" id="size-6"></td>
                <td><input type="text" name="size_6_value" id="size-6-value"></td>
                <td><input type="text" name="size_6_min" id="size-6-min"></td>
                <td><input type="text" name="size_6_max" id="size-6-max"></td>
                <td><input type="text" name="size_6_measure_1" id="size-6-measure-1"></td>
                <td><input type="text" name="size_6_measure_2" id="size-6-measure-2"></td>
                <td><input type="text" name="size_6_measure_3" id="size-6-measure-3"></td>
                <td><input type="text" name="size_6_measure_4" id="size-6-measure-4"></td>
                <td><input type="text" name="size_6_measure_5" id="size-6-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_6_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_6_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_6_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_6_note" id="size-6-note"></textarea></td>
            </tr>
            <!-- 继续添加第7-30行，格式相同 -->
            <!-- 为了简洁，这里省略了第7-29行的代码，实际使用时需要完整添加 -->
            <tr class="custom-row-height dynamic-size-row" id="size-row-7" style="display: none;">
                <td class="align-center">7</td>
                <td data-id="size-7" data-image="true"><input type="text" name="size_7_position" id="size-7"></td>
                <td><input type="text" name="size_7_value" id="size-7-value"></td>
                <td><input type="text" name="size_7_min" id="size-7-min"></td>
                <td><input type="text" name="size_7_max" id="size-7-max"></td>
                <td><input type="text" name="size_7_measure_1" id="size-7-measure-1"></td>
                <td><input type="text" name="size_7_measure_2" id="size-7-measure-2"></td>
                <td><input type="text" name="size_7_measure_3" id="size-7-measure-3"></td>
                <td><input type="text" name="size_7_measure_4" id="size-7-measure-4"></td>
                <td><input type="text" name="size_7_measure_5" id="size-7-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_7_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_7_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_7_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_7_note" id="size-7-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-8" style="display: none;">
                <td class="align-center">8</td>
                <td data-id="size-8" data-image="true"><input type="text" name="size_8_position" id="size-8"></td>
                <td><input type="text" name="size_8_value" id="size-8-value"></td>
                <td><input type="text" name="size_8_min" id="size-8-min"></td>
                <td><input type="text" name="size_8_max" id="size-8-max"></td>
                <td><input type="text" name="size_8_measure_1" id="size-8-measure-1"></td>
                <td><input type="text" name="size_8_measure_2" id="size-8-measure-2"></td>
                <td><input type="text" name="size_8_measure_3" id="size-8-measure-3"></td>
                <td><input type="text" name="size_8_measure_4" id="size-8-measure-4"></td>
                <td><input type="text" name="size_8_measure_5" id="size-8-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_8_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_8_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_8_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_8_note" id="size-8-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-9" style="display: none;">
                <td class="align-center">9</td>
                <td data-id="size-9" data-image="true"><input type="text" name="size_9_position" id="size-9"></td>
                <td><input type="text" name="size_9_value" id="size-9-value"></td>
                <td><input type="text" name="size_9_min" id="size-9-min"></td>
                <td><input type="text" name="size_9_max" id="size-9-max"></td>
                <td><input type="text" name="size_9_measure_1" id="size-9-measure-1"></td>
                <td><input type="text" name="size_9_measure_2" id="size-9-measure-2"></td>
                <td><input type="text" name="size_9_measure_3" id="size-9-measure-3"></td>
                <td><input type="text" name="size_9_measure_4" id="size-9-measure-4"></td>
                <td><input type="text" name="size_9_measure_5" id="size-9-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_9_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_9_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_9_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_9_note" id="size-9-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-10" style="display: none;">
                <td class="align-center">10</td>
                <td data-id="size-10" data-image="true"><input type="text" name="size_10_position" id="size-10"></td>
                <td><input type="text" name="size_10_value" id="size-10-value"></td>
                <td><input type="text" name="size_10_min" id="size-10-min"></td>
                <td><input type="text" name="size_10_max" id="size-10-max"></td>
                <td><input type="text" name="size_10_measure_1" id="size-10-measure-1"></td>
                <td><input type="text" name="size_10_measure_2" id="size-10-measure-2"></td>
                <td><input type="text" name="size_10_measure_3" id="size-10-measure-3"></td>
                <td><input type="text" name="size_10_measure_4" id="size-10-measure-4"></td>
                <td><input type="text" name="size_10_measure_5" id="size-10-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_10_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_10_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_10_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_10_note" id="size-10-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-11" style="display: none;">
                <td class="align-center">11</td>
                <td data-id="size-11" data-image="true"><input type="text" name="size_11_position" id="size-11"></td>
                <td><input type="text" name="size_11_value" id="size-11-value"></td>
                <td><input type="text" name="size_11_min" id="size-11-min"></td>
                <td><input type="text" name="size_11_max" id="size-11-max"></td>
                <td><input type="text" name="size_11_measure_1" id="size-11-measure-1"></td>
                <td><input type="text" name="size_11_measure_2" id="size-11-measure-2"></td>
                <td><input type="text" name="size_11_measure_3" id="size-11-measure-3"></td>
                <td><input type="text" name="size_11_measure_4" id="size-11-measure-4"></td>
                <td><input type="text" name="size_11_measure_5" id="size-11-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_11_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_11_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_11_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_11_note" id="size-11-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-12" style="display: none;">
                <td class="align-center">12</td>
                <td data-id="size-12" data-image="true"><input type="text" name="size_12_position" id="size-12"></td>
                <td><input type="text" name="size_12_value" id="size-12-value"></td>
                <td><input type="text" name="size_12_min" id="size-12-min"></td>
                <td><input type="text" name="size_12_max" id="size-12-max"></td>
                <td><input type="text" name="size_12_measure_1" id="size-12-measure-1"></td>
                <td><input type="text" name="size_12_measure_2" id="size-12-measure-2"></td>
                <td><input type="text" name="size_12_measure_3" id="size-12-measure-3"></td>
                <td><input type="text" name="size_12_measure_4" id="size-12-measure-4"></td>
                <td><input type="text" name="size_12_measure_5" id="size-12-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_12_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_12_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_12_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_12_note" id="size-12-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-13" style="display: none;">
                <td class="align-center">13</td>
                <td data-id="size-13" data-image="true"><input type="text" name="size_13_position" id="size-13"></td>
                <td><input type="text" name="size_13_value" id="size-13-value"></td>
                <td><input type="text" name="size_13_min" id="size-13-min"></td>
                <td><input type="text" name="size_13_max" id="size-13-max"></td>
                <td><input type="text" name="size_13_measure_1" id="size-13-measure-1"></td>
                <td><input type="text" name="size_13_measure_2" id="size-13-measure-2"></td>
                <td><input type="text" name="size_13_measure_3" id="size-13-measure-3"></td>
                <td><input type="text" name="size_13_measure_4" id="size-13-measure-4"></td>
                <td><input type="text" name="size_13_measure_5" id="size-13-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_13_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_13_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_13_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_13_note" id="size-13-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-14" style="display: none;">
                <td class="align-center">14</td>
                <td data-id="size-14" data-image="true"><input type="text" name="size_14_position" id="size-14"></td>
                <td><input type="text" name="size_14_value" id="size-14-value"></td>
                <td><input type="text" name="size_14_min" id="size-14-min"></td>
                <td><input type="text" name="size_14_max" id="size-14-max"></td>
                <td><input type="text" name="size_14_measure_1" id="size-14-measure-1"></td>
                <td><input type="text" name="size_14_measure_2" id="size-14-measure-2"></td>
                <td><input type="text" name="size_14_measure_3" id="size-14-measure-3"></td>
                <td><input type="text" name="size_14_measure_4" id="size-14-measure-4"></td>
                <td><input type="text" name="size_14_measure_5" id="size-14-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_14_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_14_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_14_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_14_note" id="size-14-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-15" style="display: none;">
                <td class="align-center">15</td>
                <td data-id="size-15" data-image="true"><input type="text" name="size_15_position" id="size-15"></td>
                <td><input type="text" name="size_15_value" id="size-15-value"></td>
                <td><input type="text" name="size_15_min" id="size-15-min"></td>
                <td><input type="text" name="size_15_max" id="size-15-max"></td>
                <td><input type="text" name="size_15_measure_1" id="size-15-measure-1"></td>
                <td><input type="text" name="size_15_measure_2" id="size-15-measure-2"></td>
                <td><input type="text" name="size_15_measure_3" id="size-15-measure-3"></td>
                <td><input type="text" name="size_15_measure_4" id="size-15-measure-4"></td>
                <td><input type="text" name="size_15_measure_5" id="size-15-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_15_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_15_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_15_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_15_note" id="size-15-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-16" style="display: none;">
                <td class="align-center">16</td>
                <td data-id="size-16" data-image="true"><input type="text" name="size_16_position" id="size-16"></td>
                <td><input type="text" name="size_16_value" id="size-16-value"></td>
                <td><input type="text" name="size_16_min" id="size-16-min"></td>
                <td><input type="text" name="size_16_max" id="size-16-max"></td>
                <td><input type="text" name="size_16_measure_1" id="size-16-measure-1"></td>
                <td><input type="text" name="size_16_measure_2" id="size-16-measure-2"></td>
                <td><input type="text" name="size_16_measure_3" id="size-16-measure-3"></td>
                <td><input type="text" name="size_16_measure_4" id="size-16-measure-4"></td>
                <td><input type="text" name="size_16_measure_5" id="size-16-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_16_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_16_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_16_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_16_note" id="size-16-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-17" style="display: none;">
                <td class="align-center">17</td>
                <td data-id="size-17" data-image="true"><input type="text" name="size_17_position" id="size-17"></td>
                <td><input type="text" name="size_17_value" id="size-17-value"></td>
                <td><input type="text" name="size_17_min" id="size-17-min"></td>
                <td><input type="text" name="size_17_max" id="size-17-max"></td>
                <td><input type="text" name="size_17_measure_1" id="size-17-measure-1"></td>
                <td><input type="text" name="size_17_measure_2" id="size-17-measure-2"></td>
                <td><input type="text" name="size_17_measure_3" id="size-17-measure-3"></td>
                <td><input type="text" name="size_17_measure_4" id="size-17-measure-4"></td>
                <td><input type="text" name="size_17_measure_5" id="size-17-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_17_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_17_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_17_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_17_note" id="size-17-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-18" style="display: none;">
                <td class="align-center">18</td>
                <td data-id="size-18" data-image="true"><input type="text" name="size_18_position" id="size-18"></td>
                <td><input type="text" name="size_18_value" id="size-18-value"></td>
                <td><input type="text" name="size_18_min" id="size-18-min"></td>
                <td><input type="text" name="size_18_max" id="size-18-max"></td>
                <td><input type="text" name="size_18_measure_1" id="size-18-measure-1"></td>
                <td><input type="text" name="size_18_measure_2" id="size-18-measure-2"></td>
                <td><input type="text" name="size_18_measure_3" id="size-18-measure-3"></td>
                <td><input type="text" name="size_18_measure_4" id="size-18-measure-4"></td>
                <td><input type="text" name="size_18_measure_5" id="size-18-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_18_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_18_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_18_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_18_note" id="size-18-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-19" style="display: none;">
                <td class="align-center">19</td>
                <td data-id="size-19" data-image="true"><input type="text" name="size_19_position" id="size-19"></td>
                <td><input type="text" name="size_19_value" id="size-19-value"></td>
                <td><input type="text" name="size_19_min" id="size-19-min"></td>
                <td><input type="text" name="size_19_max" id="size-19-max"></td>
                <td><input type="text" name="size_19_measure_1" id="size-19-measure-1"></td>
                <td><input type="text" name="size_19_measure_2" id="size-19-measure-2"></td>
                <td><input type="text" name="size_19_measure_3" id="size-19-measure-3"></td>
                <td><input type="text" name="size_19_measure_4" id="size-19-measure-4"></td>
                <td><input type="text" name="size_19_measure_5" id="size-19-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_19_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_19_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_19_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_19_note" id="size-19-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-20" style="display: none;">
                <td class="align-center">20</td>
                <td data-id="size-20" data-image="true"><input type="text" name="size_20_position" id="size-20"></td>
                <td><input type="text" name="size_20_value" id="size-20-value"></td>
                <td><input type="text" name="size_20_min" id="size-20-min"></td>
                <td><input type="text" name="size_20_max" id="size-20-max"></td>
                <td><input type="text" name="size_20_measure_1" id="size-20-measure-1"></td>
                <td><input type="text" name="size_20_measure_2" id="size-20-measure-2"></td>
                <td><input type="text" name="size_20_measure_3" id="size-20-measure-3"></td>
                <td><input type="text" name="size_20_measure_4" id="size-20-measure-4"></td>
                <td><input type="text" name="size_20_measure_5" id="size-20-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_20_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_20_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_20_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_20_note" id="size-20-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-21" style="display: none;">
                <td class="align-center">21</td>
                <td data-id="size-21" data-image="true"><input type="text" name="size_21_position" id="size-21"></td>
                <td><input type="text" name="size_21_value" id="size-21-value"></td>
                <td><input type="text" name="size_21_min" id="size-21-min"></td>
                <td><input type="text" name="size_21_max" id="size-21-max"></td>
                <td><input type="text" name="size_21_measure_1" id="size-21-measure-1"></td>
                <td><input type="text" name="size_21_measure_2" id="size-21-measure-2"></td>
                <td><input type="text" name="size_21_measure_3" id="size-21-measure-3"></td>
                <td><input type="text" name="size_21_measure_4" id="size-21-measure-4"></td>
                <td><input type="text" name="size_21_measure_5" id="size-21-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_21_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_21_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_21_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_21_note" id="size-21-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-22" style="display: none;">
                <td class="align-center">22</td>
                <td data-id="size-22" data-image="true"><input type="text" name="size_22_position" id="size-22"></td>
                <td><input type="text" name="size_22_value" id="size-22-value"></td>
                <td><input type="text" name="size_22_min" id="size-22-min"></td>
                <td><input type="text" name="size_22_max" id="size-22-max"></td>
                <td><input type="text" name="size_22_measure_1" id="size-22-measure-1"></td>
                <td><input type="text" name="size_22_measure_2" id="size-22-measure-2"></td>
                <td><input type="text" name="size_22_measure_3" id="size-22-measure-3"></td>
                <td><input type="text" name="size_22_measure_4" id="size-22-measure-4"></td>
                <td><input type="text" name="size_22_measure_5" id="size-22-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_22_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_22_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_22_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_22_note" id="size-22-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-23" style="display: none;">
                <td class="align-center">23</td>
                <td data-id="size-23" data-image="true"><input type="text" name="size_23_position" id="size-23"></td>
                <td><input type="text" name="size_23_value" id="size-23-value"></td>
                <td><input type="text" name="size_23_min" id="size-23-min"></td>
                <td><input type="text" name="size_23_max" id="size-23-max"></td>
                <td><input type="text" name="size_23_measure_1" id="size-23-measure-1"></td>
                <td><input type="text" name="size_23_measure_2" id="size-23-measure-2"></td>
                <td><input type="text" name="size_23_measure_3" id="size-23-measure-3"></td>
                <td><input type="text" name="size_23_measure_4" id="size-23-measure-4"></td>
                <td><input type="text" name="size_23_measure_5" id="size-23-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_23_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_23_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_23_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_23_note" id="size-23-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-24" style="display: none;">
                <td class="align-center">24</td>
                <td data-id="size-24" data-image="true"><input type="text" name="size_24_position" id="size-24"></td>
                <td><input type="text" name="size_24_value" id="size-24-value"></td>
                <td><input type="text" name="size_24_min" id="size-24-min"></td>
                <td><input type="text" name="size_24_max" id="size-24-max"></td>
                <td><input type="text" name="size_24_measure_1" id="size-24-measure-1"></td>
                <td><input type="text" name="size_24_measure_2" id="size-24-measure-2"></td>
                <td><input type="text" name="size_24_measure_3" id="size-24-measure-3"></td>
                <td><input type="text" name="size_24_measure_4" id="size-24-measure-4"></td>
                <td><input type="text" name="size_24_measure_5" id="size-24-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_24_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_24_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_24_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_24_note" id="size-24-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-25" style="display: none;">
                <td class="align-center">25</td>
                <td data-id="size-25" data-image="true"><input type="text" name="size_25_position" id="size-25"></td>
                <td><input type="text" name="size_25_value" id="size-25-value"></td>
                <td><input type="text" name="size_25_min" id="size-25-min"></td>
                <td><input type="text" name="size_25_max" id="size-25-max"></td>
                <td><input type="text" name="size_25_measure_1" id="size-25-measure-1"></td>
                <td><input type="text" name="size_25_measure_2" id="size-25-measure-2"></td>
                <td><input type="text" name="size_25_measure_3" id="size-25-measure-3"></td>
                <td><input type="text" name="size_25_measure_4" id="size-25-measure-4"></td>
                <td><input type="text" name="size_25_measure_5" id="size-25-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_25_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_25_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_25_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_25_note" id="size-25-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-26" style="display: none;">
                <td class="align-center">26</td>
                <td data-id="size-26" data-image="true"><input type="text" name="size_26_position" id="size-26"></td>
                <td><input type="text" name="size_26_value" id="size-26-value"></td>
                <td><input type="text" name="size_26_min" id="size-26-min"></td>
                <td><input type="text" name="size_26_max" id="size-26-max"></td>
                <td><input type="text" name="size_26_measure_1" id="size-26-measure-1"></td>
                <td><input type="text" name="size_26_measure_2" id="size-26-measure-2"></td>
                <td><input type="text" name="size_26_measure_3" id="size-26-measure-3"></td>
                <td><input type="text" name="size_26_measure_4" id="size-26-measure-4"></td>
                <td><input type="text" name="size_26_measure_5" id="size-26-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_26_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_26_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_26_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_26_note" id="size-26-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-27" style="display: none;">
                <td class="align-center">27</td>
                <td data-id="size-27" data-image="true"><input type="text" name="size_27_position" id="size-27"></td>
                <td><input type="text" name="size_27_value" id="size-27-value"></td>
                <td><input type="text" name="size_27_min" id="size-27-min"></td>
                <td><input type="text" name="size_27_max" id="size-27-max"></td>
                <td><input type="text" name="size_27_measure_1" id="size-27-measure-1"></td>
                <td><input type="text" name="size_27_measure_2" id="size-27-measure-2"></td>
                <td><input type="text" name="size_27_measure_3" id="size-27-measure-3"></td>
                <td><input type="text" name="size_27_measure_4" id="size-27-measure-4"></td>
                <td><input type="text" name="size_27_measure_5" id="size-27-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_27_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_27_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_27_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_27_note" id="size-27-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-28" style="display: none;">
                <td class="align-center">28</td>
                <td data-id="size-28" data-image="true"><input type="text" name="size_28_position" id="size-28"></td>
                <td><input type="text" name="size_28_value" id="size-28-value"></td>
                <td><input type="text" name="size_28_min" id="size-28-min"></td>
                <td><input type="text" name="size_28_max" id="size-28-max"></td>
                <td><input type="text" name="size_28_measure_1" id="size-28-measure-1"></td>
                <td><input type="text" name="size_28_measure_2" id="size-28-measure-2"></td>
                <td><input type="text" name="size_28_measure_3" id="size-28-measure-3"></td>
                <td><input type="text" name="size_28_measure_4" id="size-28-measure-4"></td>
                <td><input type="text" name="size_28_measure_5" id="size-28-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_28_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_28_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_28_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_28_note" id="size-28-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-29" style="display: none;">
                <td class="align-center">29</td>
                <td data-id="size-29" data-image="true"><input type="text" name="size_29_position" id="size-29"></td>
                <td><input type="text" name="size_29_value" id="size-29-value"></td>
                <td><input type="text" name="size_29_min" id="size-29-min"></td>
                <td><input type="text" name="size_29_max" id="size-29-max"></td>
                <td><input type="text" name="size_29_measure_1" id="size-29-measure-1"></td>
                <td><input type="text" name="size_29_measure_2" id="size-29-measure-2"></td>
                <td><input type="text" name="size_29_measure_3" id="size-29-measure-3"></td>
                <td><input type="text" name="size_29_measure_4" id="size-29-measure-4"></td>
                <td><input type="text" name="size_29_measure_5" id="size-29-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_29_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_29_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_29_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_29_note" id="size-29-note"></textarea></td>
            </tr>
            <tr class="custom-row-height dynamic-size-row" id="size-row-30" style="display: none;">
                <td class="align-center">30</td>
                <td data-id="size-30" data-image="true"><input type="text" name="size_30_position" id="size-30"></td>
                <td><input type="text" name="size_30_value" id="size-30-value"></td>
                <td><input type="text" name="size_30_min" id="size-30-min"></td>
                <td><input type="text" name="size_30_max" id="size-30-max"></td>
                <td><input type="text" name="size_30_measure_1" id="size-30-measure-1"></td>
                <td><input type="text" name="size_30_measure_2" id="size-30-measure-2"></td>
                <td><input type="text" name="size_30_measure_3" id="size-30-measure-3"></td>
                <td><input type="text" name="size_30_measure_4" id="size-30-measure-4"></td>
                <td><input type="text" name="size_30_measure_5" id="size-30-measure-5"></td>
                <td>
                    <label><input type="radio" name="size_30_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_30_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_30_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_30_note" id="size-30-note"></textarea></td>
            </tr>



            <!-- ================= 外观检查 ================= -->
            <tr class="module-title">
                <td rowspan="5" style="width: 30px;">二、外观</td>
                <td class="col-no align-center">NO</td>
                <td colspan="9" class="align-center">要求描述</td>
                <td class="col-check align-center col-compliance">符合性</td>
                <td class="col-note align-center" colspan="2">注意事项</td>
            </tr>
            <!-- 外观条目 -->
            <tr>
                <td class="align-center">1</td>
                <td colspan="9" class="align-left">表面刮痕、凹痕、裂纹、沙孔等不良符合《外观标准》要求</td>
                <td>
                    <label><input type="radio" name="appearance_1_check" value="合格"> 合格</label>
                    <label><input type="radio" name="appearance_1_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="appearance_1_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="appearance_1_note"></textarea></td>
            </tr>
            <tr>
                <td class="align-center">2</td>
                <td colspan="9" class="align-left">不能出现表面生锈、披锋刮手、焊接无脱焊、表面抛光纹路、光泽度符合要求</td>
                <td>
                    <label><input type="radio" name="appearance_2_check" value="合格"> 合格</label>
                    <label><input type="radio" name="appearance_2_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="appearance_2_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="appearance_2_note"></textarea></td>
            </tr>
            <tr>
                <td class="align-center">3</td>
                <td colspan="9" class="align-left">无明显的气味</td>
                <td>
                    <label><input type="radio" name="appearance_3_check" value="合格"> 合格</label>
                    <label><input type="radio" name="appearance_3_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="appearance_3_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="appearance_3_note"></textarea></td>
            </tr>
            <tr>
                <td class="align-center">4</td>
                <td colspan="9" class="align-left">
                    <textarea name="appearance_4_other" id="appearance-other" placeholder="其他/特殊要求："></textarea>
                </td>
                <td>
                    <label><input type="radio" name="appearance_4_check" value="合格"> 合格</label>
                    <label><input type="radio" name="appearance_4_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="appearance_4_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="appearance_4_note"></textarea></td>
            </tr>
            <tr class="module-title">
                <td rowspan="7" class="align-center">三、功能/可靠性</td>
                <td class="align-center">NO.</td>
                <td colspan="9" class="align-center">要求描述</td>
                <td>符合性</td>
                <td colspan="2">注意事项</td>
            </tr>
            <tr>
                <td class="align-center">1</td>
                <td colspan="9" class="align-left">牙管内、外螺牙顺畅（管缝无高起）；弯管的折弯角度符合要求</td>
                <td>
                    <label><input type="radio" name="function_1_check" value="合格"> 合格</label>
                    <label><input type="radio" name="function_1_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="function_1_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="function_1_note"></textarea></td>
            </tr>
            <tr>
                <td class="align-center">2</td>
                <td colspan="9" class="align-left">部件的认证标识参数符合要求</td>
                <td>
                    <label><input type="radio" name="function_2_check" value="合格"> 合格</label>
                    <label><input type="radio" name="function_2_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="function_2_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="function_2_note"></textarea></td>
            </tr>
            <tr>
                <td class="align-center">3</td>
                <td colspan="9" class="align-left">使用配合件进行试装配检验</td>
                <td>
                    <label><input type="radio" name="function_3_check" value="合格"> 合格</label>
                    <label><input type="radio" name="function_3_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="function_3_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="function_3_note"></textarea></td>
            </tr>
            <tr>
                <td class="align-center">4</td>
                <td colspan="9" class="align-left">
                    电气性能：光电参数、耐压测试、亮灯性能、老化测试： 
                    <input type="text" name="function_4_burnin" style="width: 300px;display: inline-block;border: 1px solid #000;"><br><br>
                    电阻、电感、电容等关键电气参数：
                    <input type="text" name="function_4_electrical" style="width: 380px;display: inline-block;border: 1px solid #000;">
                </td>
                <td>
                    <label><input type="radio" name="function_4_check" value="合格"> 合格</label>
                    <label><input type="radio" name="function_4_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="function_4_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="function_4_note"></textarea></td>
            </tr>
            <tr>
                <td class="align-center">5</td>
                <td colspan="9" class="align-left" style="font-size: 13px;">
                    <!-- 添加复选框 -->
                    <input type="checkbox" id="function_5_tests_salt_fog" name="function_5_tests[]" value="盐雾测试">
                    <label for="function_5_tests_salt_fog">盐雾测试</label>
                    
                    <input type="checkbox" id="function_5_tests_thickness" name="function_5_tests[]" value="厚度测试">
                    <label for="function_5_tests_thickness">厚度测试</label>
                    
                    <input type="checkbox" id="function_5_tests_flame_retardant" name="function_5_tests[]" value="阻燃测试">
                    <label for="function_5_tests_flame_retardant">阻燃测试</label>
                    
                    <input type="checkbox" id="function_5_tests_hardness" name="function_5_tests[]" value="硬度测试">
                    <label for="function_5_tests_hardness">硬度测试</label>
                    
                    <input type="checkbox" id="function_5_tests_adhesion" name="function_5_tests[]" value="附着力测试">
                    <label for="function_5_tests_adhesion">附着力测试</label><br>
                    
                    <input type="checkbox" id="function_5_tests_environment" name="function_5_tests[]" value="环境测试">
                    <label for="function_5_tests_environment">环境测试</label>
                    
                    <input type="checkbox" id="function_5_tests_stress" name="function_5_tests[]" value="应力测试">
                    <label for="function_5_tests_stress">应力测试</label>
                    
                    <input type="checkbox" id="function_5_tests_torque" name="function_5_tests[]" value="扭力测试">
                    <label for="function_5_tests_torque">扭力测试</label>
                    
                    <input type="checkbox" id="function_5_tests_impact" name="function_5_tests[]" value="冲击测试">
                    <label for="function_5_tests_impact">冲击测试</label>
                    
                    <input type="checkbox" id="function_5_tests_pull_force" name="function_5_tests[]" value="推拉力测试">
                    <label for="function_5_tests_pull_force">推拉力测试</label><br>
                    
                    <input type="checkbox" id="function_5_tests_scratch_resistance" name="function_5_tests[]" value="耐刮擦性测试">
                    <label for="function_5_tests_scratch_resistance">耐刮擦性测试</label>
                    
                    <input type="checkbox" id="function_5_tests_other" name="function_5_tests[]" value="其他测试">
                    <label for="function_5_tests_other">其他测试：</label>
                    <input type="text" id="function_5_other_test" name="function_5_other_test" style="width: 300px;display: none;"placeholder="请填写其他测试内容">
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // 获取复选框元素
                            const checkbox = document.getElementById('function_5_tests_other');
                            const textbox = document.getElementById('function_5_other_test');
                    
                            // 监听复选框的状态变化
                            checkbox.addEventListener('change', function() {
                                // 根据复选框是否被选中，显示或隐藏文本框
                                textbox.style.display = this.checked ? 'inline-block' : 'none';
                            });
                        });
                    </script>
                </td>
                <td>
                    <label><input type="radio" name="function_5_check" value="合格"> 合格</label>
                    <label><input type="radio" name="function_5_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="function_5_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="function_5_note"></textarea></td>
            </tr>
            <tr>
                <td class="align-center">6</td>
                <td colspan="9" class="align-left"><textarea name="function_6_other" placeholder="其他/特殊要求："></textarea></td>

                <td>
                    <label><input type="radio" name="function_6_check" value="合格"> 合格</label>
                    <label><input type="radio" name="function_6_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="function_6_check" value="/" checked> /</label>
                </td>
                <td colspan="2"><textarea name="function_6_note"></textarea></td>
            </tr>

            <!-- ================= 补充问题点 ================= -->
            <tr class="module-title">
                <td colspan="14">四、补充问题点/题点</td>
            </tr>
            <tr>
                <td colspan="4" class="image-upload-cell">
                    <div class="image-upload-box">
                        <input type="file" class="image-input" data-question="1" accept="image/*">
                        <div class="upload-overlay">
                            点击上传或粘贴截图
                            <span class="upload-shortcut">支持拖放图片或粘贴</span>
                        </div>
                        <img class="preview-image">
                        <button class="delete-btn" data-question="1" type="button">×</button>
                        <button class="paste-button" data-question="1" type="button">📋</button>
                        <button class="copy-button" type="button">📑</button>
                        <button class="upload-button" type="button">📂</button>
                    </div>
                </td>
                <td colspan="5" class="image-upload-cell">
                    <div class="image-upload-box">
                        <input type="file" class="image-input" data-question="2" accept="image/*">
                        <div class="upload-overlay">
                            点击上传或粘贴截图
                            <span class="upload-shortcut">支持拖放图片或粘贴</span>
                        </div>
                        <img class="preview-image">
                        <button class="delete-btn" data-question="2" type="button">×</button>
                        <button class="paste-button" data-question="2" type="button">📋</button>
                        <button class="copy-button" type="button">📑</button>
                        <button class="upload-button" type="button">📂</button>
                    </div>
                </td>
                <td colspan="5" class="image-upload-cell">
                    <div class="image-upload-box">
                        <input type="file" class="image-input" data-question="3" accept="image/*">
                        <div class="upload-overlay">
                            点击上传或粘贴截图
                            <span class="upload-shortcut">支持拖放图片或粘贴</span>
                        </div>
                        <img class="preview-image">
                        <button class="delete-btn" data-question="3" type="button">×</button>
                        <button class="paste-button" data-question="3" type="button">📋</button>
                        <button class="copy-button" type="button">📑</button>
                        <button class="upload-button" type="button">📂</button>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="4" style="height: 20px;">
                    <textarea id="question_1" name="question_1"></textarea>
                </td>
                <td colspan="5" style="height: 20px;">
                    <textarea id="question_2" name="question_2"></textarea>
                </td>
                <td colspan="5" style="height: 20px;">
                    <textarea id="question_3" name="question_3"></textarea>
                </td>
            </tr>
            <tr>
                <td colspan="4" class="image-upload-cell">
                    <div class="image-upload-box">
                        <input type="file" class="image-input" data-question="4" accept="image/*">
                        <div class="upload-overlay">
                            点击上传或粘贴截图
                            <span class="upload-shortcut">支持拖放图片或粘贴</span>
                        </div>
                        <img class="preview-image">
                        <button class="delete-btn" data-question="4" type="button">×</button>
                        <button class="paste-button" data-question="4" type="button">📋</button>
                        <button class="copy-button" type="button">📑</button>
                        <button class="upload-button" type="button">📂</button>
                    </div>
                </td>
                <td colspan="5" class="image-upload-cell">
                    <div class="image-upload-box">
                        <input type="file" class="image-input" data-question="5" accept="image/*">
                        <div class="upload-overlay">
                            点击上传或粘贴截图
                            <span class="upload-shortcut">支持拖放图片或粘贴</span>
                        </div>
                        <img class="preview-image">
                        <button class="delete-btn" data-question="5" type="button">×</button>
                        <button class="paste-button" data-question="5" type="button">📋</button>
                        <button class="copy-button" type="button">📑</button>
                        <button class="upload-button" type="button">📂</button>
                    </div>
                </td>
                <td colspan="5" class="image-upload-cell">
                    <div class="image-upload-box">
                        <input type="file" class="image-input" data-question="6" accept="image/*">
                        <div class="upload-overlay">
                            点击上传或粘贴截图
                            <span class="upload-shortcut">支持拖放图片或粘贴</span>
                        </div>
                        <img class="preview-image">
                        <button class="delete-btn" data-question="6" type="button">×</button>
                        <button class="paste-button" data-question="6" type="button">📋</button>
                        <button class="copy-button" type="button">📑</button>
                        <button class="upload-button" type="button">📂</button>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="4" style="height: 20px;">
                    <textarea id="question_4" name="question_4"></textarea>
                </td>
                <td colspan="5" style="height: 20px;">
                    <textarea id="question_5" name="question_5"></textarea>
                </td>
                <td colspan="5" style="height: 20px;">
                    <textarea id="question_6" name="question_6"></textarea>
                </td>
            </tr>

            <tr>
                <td colspan="14" class="align-center">
                    <button type="button" id="add-question-btn" class="btn btn-primary">增加问题点</button>
                    <button type="button" id="remove-question-btn" class="btn btn-danger">删除问题点</button>
                </td>
            </tr>

            <!-- ================= 最终判定 ================= -->
            <tr>
                <td rowspan="2" colspan="2" class="align-center">最终判定</td>
                <td colspan="10" class="align-left" style="height: 30px;font-size: 13px;">
                    <!-- 使用单选按钮实现互斥选择 -->
                    <input type="radio" name="final_judgment" value="合格" id="qualified-radio"> 
                    <label for="qualified-radio">合格</label>
                    <input type="radio" name="final_judgment" value="不合格" id="not-qualified-radio"> 
                    <label for="not-qualified-radio">不合格</label>
                </td>
                <td colspan="2" class="align-center">审核</td>
            </tr>
            <tr>
                <td colspan="1" class="align-left">意见：</td>
                <td colspan="9">
                    <textarea id="opinion" name="opinion" rows="2" cols="55"></textarea>
                </td>
                <td colspan="1">
                    <input type="text" id="review" name="review">
                </td>
                <td colspan="1"></td>
            </tr>
        </table>
    </form>

    <div class="feedback-message" id="feedback-message"></div>
    
    <!-- 添加图片放大模态框 -->
    <div id="imageModal" class="image-modal">
        <span class="close">&times;</span>
        <div class="modal-loader"></div>
        <img class="modal-content" id="modalImage">
        <div class="image-controls">
            <button class="control-btn" id="zoom-out">−</button>
            <button class="control-btn" id="reset-view">重置</button>
            <button class="control-btn" id="zoom-in">+</button>
        </div>
        <div class="tooltip" id="image-tooltip"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 使用本地时区获取今天的日期
            const now = new Date();
            const offset = now.getTimezoneOffset() * 60000; // 获取时区偏移（毫秒）
            const today = new Date(now.getTime() - offset).toISOString().split('T')[0];
            document.getElementById('date').value = today;
            
            // 重置临时编码，确保每次新增都是新的临时ID
            function resetTempId() {
                const materialNumber = document.getElementById('material-number').value.trim();
                if (materialNumber) {
                    fetch('/Material_Sample_Confirmation_Form/reset_material_uuid', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            material_number: materialNumber
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            console.log('临时编码已重置');
                        }
                    })
                    .catch(error => {
                        console.error('重置临时编码出错:', error);
                    });
                }
            }
            
            // 当用户更改物料料号时重置临时ID
            const materialNumberInput = document.getElementById('material-number');
            materialNumberInput.addEventListener('change', resetTempId);
            
            // 页面加载时尝试重置临时ID（可能由于刷新或重新打开）
            resetTempId();
            
            // 移除 Flatpickr 初始化代码
            // 检查已有的图片并更新按钮状态
            document.querySelectorAll('.image-upload-box').forEach(box => {
                const previewImage = box.querySelector('.preview-image');
                const uploadOverlay = box.querySelector('.upload-overlay');
                const deleteBtn = box.querySelector('.delete-btn');
                
                // 检查是否已有图片
                if (previewImage.src && previewImage.src !== window.location.href) {
                    previewImage.style.display = 'block';
                    uploadOverlay.style.display = 'none';
                    deleteBtn.style.display = 'block';
                    box.classList.add('has-image');
                } else {
                    previewImage.style.display = 'none';
                    uploadOverlay.style.display = 'block';
                    deleteBtn.style.display = 'none';
                    box.classList.remove('has-image');
                }
            });
            
            // 全局变量跟踪最后点击和最后悬停的上传框
            window.lastClickedBox = null;
            window.lastHoveredBox = null;
            
            // 物料编码验证函数
            function validateMaterialNumber() {
                const materialNumber = document.getElementById('material-number').value.trim();
                if (!materialNumber) {
                    showFeedback('请先填写物料料号！', 'error');
                    return false;
                }
                return true;
            }
            
            // 初始化所有现有的问题点图片上传框
            const allQuestionBoxes = document.querySelectorAll('.image-upload-cell .image-upload-box');
            allQuestionBoxes.forEach(box => {
                // 添加复制按钮（如果不存在）
                if (!box.querySelector('.copy-button')) {
                    const copyButton = document.createElement('button');
                    copyButton.className = 'copy-button';
                    copyButton.innerHTML = '📑';
                    copyButton.type = 'button';
                    
                    // 默认隐藏复制按钮，只在有图片时显示
                    copyButton.style.display = 'none';
                    
                    // 添加悬停效果
                    copyButton.addEventListener('mouseover', function() {
                        this.innerHTML = '📑 复制';
                        this.style.width = '70px';
                        this.style.paddingLeft = '8px';
                    });
                    
                    copyButton.addEventListener('mouseout', function() {
                        this.innerHTML = '📑';
                        this.style.width = '28px';
                        this.style.paddingLeft = '4px';
                    });
                    
                    copyButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        // 获取预览图片
                        const previewImage = box.querySelector('.preview-image');
                        if (!previewImage || !previewImage.src || previewImage.src === window.location.href) {
                            showFeedback('没有图片可复制', 'error');
                            return;
                        }
                        
                        // 复制图片到剪贴板
                        copyImageToClipboard(previewImage);
                    });
                    
                    box.appendChild(copyButton);
                }
                
                // 添加上传按钮（如果不存在）
                if (!box.querySelector('.upload-button')) {
                    const uploadButton = document.createElement('button');
                    uploadButton.className = 'upload-button';
                    uploadButton.innerHTML = '📂';
                    uploadButton.type = 'button';
                    
                    // 添加悬停效果
                    uploadButton.addEventListener('mouseover', function() {
                        this.innerHTML = '📂 上传';
                        this.style.width = '70px';
                        this.style.paddingLeft = '8px';
                    });
                    
                    uploadButton.addEventListener('mouseout', function() {
                        this.innerHTML = '📂';
                        this.style.width = '28px';
                        this.style.paddingLeft = '4px';
                    });
                    
                    uploadButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        if (!validateMaterialNumber()) {
                            return;
                        }
                        
                        window.lastClickedBox = box;
                        
                        // 点击时触发文件选择框
                        const fileInput = box.querySelector('.image-input');
                        if (fileInput) {
                            fileInput.click();
                        }
                    });
                    
                    box.appendChild(uploadButton);
                }
                
                // 如果已有图片，显示复制按钮并添加放大功能
                const previewImage = box.querySelector('.preview-image');
                if (previewImage && previewImage.src && previewImage.src !== window.location.href) {
                    const copyButton = box.querySelector('.copy-button');
                    if (copyButton) {
                        copyButton.style.display = 'block';
                    }
                    
                    // 为图片添加放大功能
                    addZoomFunctionality(previewImage);
                }
            });
            
            // 为所有上传框添加事件
            document.querySelectorAll('.image-upload-box').forEach(box => {
                // 1. 点击事件 - 触发文件选择
                box.addEventListener('click', function(e) {
                    // 如果点击的是删除按钮或粘贴按钮或其他按钮，不触发文件选择
                    if (e.target.classList.contains('delete-btn') || 
                        e.target.classList.contains('paste-button') ||
                        e.target.classList.contains('copy-button') ||
                        e.target.classList.contains('upload-button')) {
                        return;
                    }
                    
                    // 检查物料编码
                    if (!validateMaterialNumber()) {
                        return;
                    }
                    
                    // 更新最后点击的上传框
                    window.lastClickedBox = this;
                    
                    // 触发文件选择
                    const fileInput = this.querySelector('.image-input');
                    if (fileInput) {
                        fileInput.click();
                    }
                });
                
                // 2. 鼠标悬停事件
                box.addEventListener('mouseenter', function() {
                    window.lastHoveredBox = this;
                    this.classList.add('active-hover');
                });
                
                box.addEventListener('mouseleave', function() {
                    this.classList.remove('active-hover');
                    if (window.lastHoveredBox === this) {
                        window.lastHoveredBox = null;
                    }
                });
                
                // 3. 拖放功能
                box.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.add('drag-over');
                });
                
                box.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.remove('drag-over');
                });
                
                box.addEventListener('drop', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.remove('drag-over');
                    
                    // 检查物料编码
                    if (!validateMaterialNumber()) {
                        return;
                    }
                    
                    const dt = e.dataTransfer;
                    const files = dt.files;
                    
                    if (files.length) {
                        const fileInput = this.querySelector('.image-input');
                        fileInput.files = files;
                        
                        // 触发change事件
                        const event = new Event('change', { bubbles: true });
                        fileInput.dispatchEvent(event);
                        
                        showFeedback('图片已成功拖放上传', 'success');
                    }
                });
                
                // 添加上传按钮
                if (!box.querySelector('.upload-button')) {
                    const uploadButton = document.createElement('button');
                    uploadButton.className = 'upload-button';
                    uploadButton.innerHTML = '📂';
                    uploadButton.type = 'button';
                    
                    // 添加悬停效果
                    uploadButton.addEventListener('mouseover', function() {
                        this.innerHTML = '📂 上传';
                        this.style.width = '70px';
                        this.style.paddingLeft = '8px';
                    });
                    
                    uploadButton.addEventListener('mouseout', function() {
                        this.innerHTML = '📂';
                        this.style.width = '28px';
                        this.style.paddingLeft = '4px';
                    });
                    
                    uploadButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        if (!validateMaterialNumber()) {
                            return;
                        }
                        
                        window.lastClickedBox = box;
                        
                        // 点击时触发文件选择框
                        const fileInput = box.querySelector('.image-input');
                        if (fileInput) {
                            fileInput.click();
                        }
                    });
                    
                    box.appendChild(uploadButton);
                }
                
                // 添加复制按钮
                if (!box.querySelector('.copy-button')) {
                    const copyButton = document.createElement('button');
                    copyButton.className = 'copy-button';
                    copyButton.innerHTML = '📑';
                    copyButton.type = 'button';
                    
                    // 默认隐藏复制按钮，只在有图片时显示
                    copyButton.style.display = 'none';
                    
                    // 添加悬停效果
                    copyButton.addEventListener('mouseover', function() {
                        this.innerHTML = '📑 复制';
                        this.style.width = '70px';
                        this.style.paddingLeft = '8px';
                    });
                    
                    copyButton.addEventListener('mouseout', function() {
                        this.innerHTML = '📑';
                        this.style.width = '28px';
                        this.style.paddingLeft = '4px';
                    });
                    
                    copyButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        // 获取预览图片
                        const previewImage = box.querySelector('.preview-image');
                        if (!previewImage || !previewImage.src || previewImage.src === window.location.href) {
                            showFeedback('没有图片可复制', 'error');
                            return;
                        }
                        
                        // 复制图片到剪贴板
                        copyImageToClipboard(previewImage);
                    });
                    
                    box.appendChild(copyButton);
                }
            });
            
            // 4. 文件选择变化事件
            document.querySelectorAll('.image-input').forEach(input => {
                input.addEventListener('change', function(e) {
                    // 检查物料编码
                    if (!validateMaterialNumber()) {
                        // 清空选择的文件
                        this.value = '';
                        return;
                    }
                    
                    if (this.files && this.files[0]) {
                        // 检查是否有旧图片需要先删除
                        const tempId = this.getAttribute('data-temp-id');
                        if (tempId) {
                            if (confirm('确定要删除旧图片并上传新图片吗？')) {
                                const questionNumber = this.getAttribute('data-question');
                                const materialNumber = document.getElementById('material-number').value;
                                
                                fetch('/Material_Sample_Confirmation_Form/delete_image', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({
                                        question_id: questionNumber,
                                        material_number: materialNumber
                                    })
                                })
                                .then(() => {
                                    // 删除成功后上传新图片
                                    handleImageUpload(this);
                                })
                                .catch(error => {
                                    console.error('删除旧图片错误:', error);
                                    showFeedback('删除旧图片失败，请重试', 'error');
                                });
                            } else {
                                this.value = ''; // 取消操作时清空文件选择
                            }
                        } else {
                            // 没有旧图片直接上传
                            handleImageUpload(this);
                        }
                    }
                });
            });
            
            // 5. 粘贴按钮点击事件
            document.querySelectorAll('.paste-button').forEach(button => {
                // 防止重复绑定事件
                if (button.hasAttribute('data-event-bound')) return;
                button.setAttribute('data-event-bound', 'true');
                
                // 添加悬停效果
                button.addEventListener('mouseover', function() {
                    this.innerHTML = '📋 粘贴';
                    this.style.width = '70px';
                    this.style.paddingLeft = '8px';
                });
                
                button.addEventListener('mouseout', function() {
                    this.innerHTML = '📋';
                    this.style.width = '28px';
                    this.style.paddingLeft = '4px';
                });
                
                button.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                    
                    // 检查物料编码
                    if (!validateMaterialNumber()) {
                        return;
                    }
                    
                    // 找到对应的上传框
                    const box = this.closest('.image-upload-box');
                    if (!box) return;
                    
                    // 设置为当前活动上传框
                    window.lastClickedBox = box;
                    window.lastHoveredBox = box;
                    
                    // 尝试读取剪贴板
                    if (navigator.clipboard && navigator.clipboard.read) {
                        showFeedback('正在读取剪贴板...', 'info');
                        
                        navigator.clipboard.read()
                            .then(clipboardItems => {
                                let hasImage = false;
                                
                                // 遍历所有剪贴板项
                                for (const item of clipboardItems) {
                                    for (const type of item.types) {
                                        if (type.startsWith('image/')) {
                                            hasImage = true;
                                            item.getType(type).then(blob => {
                                                const file = new File([blob], 'clipboard-image.png', { type });
                                                
                                                // 设置到文件输入框
                                                const fileInput = box.querySelector('.image-input');
                                                if (fileInput) {
                                                    const dataTransfer = new DataTransfer();
                                                    dataTransfer.items.add(file);
                                                    fileInput.files = dataTransfer.files;
                                                    
                                                    // 触发change事件
                                                    const event = new Event('change', { bubbles: true });
                                                    fileInput.dispatchEvent(event);
                                                    
                                                    showFeedback('图片已成功粘贴', 'success');
                                                }
                                            });
                                            break;
                                        }
                                    }
                                    if (hasImage) break;
                                }
                                
                                if (!hasImage) {
                                    showFeedback('剪贴板中没有图片', 'error');
                                }
                            })
                            .catch(err => {
                                console.error('无法访问剪贴板:', err);
                                showFeedback('请使用粘贴图片', 'info');
                            });
                    } else {
                        showFeedback('请使用粘贴图片', 'info');
                    }
                });
            });
            
            // 6. 全局粘贴事件处理
            document.querySelectorAll('.paste-button').forEach(button => {
                // 防止重复绑定事件
                if (button.hasAttribute('data-event-bound')) return;
                
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    // 已有的物料编码验证
                    if (!validateMaterialNumber()) return;
                    
                    // 获取对应上传框
                    const box = this.closest('.image-upload-box');
                    if (!box) return;
                    
                    // 设置当前活动框
                    window.lastClickedBox = box;
                    
                    // 读取剪贴板图片
                    navigator.clipboard.read().then(clipboardItems => {
                        let hasImage = false;
                        for (const item of clipboardItems) {
                            for (const type of item.types) {
                                if (type.startsWith('image/')) {
                                    hasImage = true;
                                    item.getType(type).then(blob => {
                                        const file = new File([blob], 'paste-image.png', {type});
                                        handleImageUpload(box, file);
                                    });
                                    break;
                                }
                            }
                            if (hasImage) break;
                        }
                        if (!hasImage) showFeedback('剪贴板无图片', 'error');
                    }).catch(err => {
                        console.error('访问剪贴板失败:', err);
                        showFeedback('无法访问剪贴板', 'error');
                    });
                });
            });
            
            // 7. 删除按钮事件处理
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('delete-btn')) {
                    const questionNumber = e.target.getAttribute('data-question');
                    const box = document.querySelector(`.image-input[data-question="${questionNumber}"]`).closest('.image-upload-box');
                    const previewImage = box.querySelector('.preview-image');
                    const uploadOverlay = box.querySelector('.upload-overlay');
                    const fileInput = box.querySelector('.image-input');
                    
                    // 获取物料料号
                    const materialNumber = document.getElementById('material-number').value;
                    
                    // 检查是否有临时ID（已上传到服务器的图片）
                    const tempId = fileInput.getAttribute('data-temp-id');
                    
                    if (tempId) {
                        // 从服务器删除图片
                        fetch('/Material_Sample_Confirmation_Form/delete_image', {
                        method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                question_id: questionNumber,
                                material_number: materialNumber,
                                temp_id: tempId // 添加临时ID作为参数
                            })
                        })
                        .then(response => {
                            // 即使服务器返回错误也清除本地图片
                            if (!response.ok) {
                                console.warn('删除图片服务器响应异常:', response.status);
                                // 继续清除本地图片
                                clearImageBox();
                                showFeedback('图片已从本地移除', 'info');
                                return null; // 不再尝试解析JSON
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data && data.success) {
                                // 清除图片
                                clearImageBox();
                                showFeedback('图片已成功删除', 'success');
                            } else if (data) {
                                // 服务器返回了错误但仍然清除本地图片
                                clearImageBox();
                                showFeedback(data.message || '图片已从本地移除', 'info');
                            }
                        })
                        .catch(error => {
                            console.error('删除图片错误:', error);
                            // 即使出错也清除本地图片
                            clearImageBox();
                            showFeedback('图片已从本地移除', 'info');
                        });
                    } else {
                        // 直接清除本地预览的图片
                        clearImageBox();
                        showFeedback('图片已删除', 'info');
                    }
                    
                    // 清除上传框的函数
                    function clearImageBox() {
                        previewImage.src = '';
                        previewImage.style.display = 'none';
                        uploadOverlay.style.display = 'block';
                        deleteBtn.style.display = 'none';
                        box.classList.remove('has-image');
                        
                        // 清除文件输入框的值
                        fileInput.value = '';
                        fileInput.removeAttribute('data-temp-id');
                    }
                    
                    e.stopPropagation(); // 防止点击删除按钮时触发上传框的点击事件
                }
            });
            
            // 表单提交前验证
            document.querySelector('form').addEventListener('submit', function(e) {
                // 验证必填字段
                const requiredFields = document.querySelectorAll('input[required], select[required], textarea[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('is-invalid');
                        
                        // 显示错误消息
                        showFeedback(`请填写${field.previousElementSibling?.textContent || '必填'}字段`, 'error');
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                }
            });
            
            // 对合格和不合格进行判断
            const radioButtons = document.querySelectorAll('input[type="radio"][name$="_check"]');
            radioButtons.forEach(radio => {
                radio.addEventListener('change', function () {
                    // 如果当前单选按钮被选中
                    if (this.checked) {
                        // 获取当前单选按钮的分组标识（去掉 _check 后的部分）
                        const groupName = this.name.replace(/_check$/, '');
                        // 找到同组的所有单选按钮
                        const groupRadios = Array.from(radioButtons).filter(rb => rb.name.startsWith(groupName));
                        // 确保同组内只有一个单选按钮被选中
                        groupRadios.forEach(rb => {
                            if (rb !== this) {
                                rb.checked = false; // 取消其他单选按钮的选中状态
                            }
                        });
                    }
                });
            });

            // 添加自动判断实测尺寸是否在范围内的功能
            function checkMeasurements(row) {
                const rowNumber = row.querySelector('td:first-child').textContent;
                const minValue = parseFloat(row.querySelector(`input[name="size_${rowNumber}_min"]`).value);
                const maxValue = parseFloat(row.querySelector(`input[name="size_${rowNumber}_max"]`).value);
                
                // 获取所有实测值
                const measurements = [];
                for (let i = 1; i <= 5; i++) {
                    const measureValue = parseFloat(row.querySelector(`input[name="size_${rowNumber}_measure_${i}"]`).value);
                    if (!isNaN(measureValue)) {
                        measurements.push(measureValue);
                    }
                }

                // 如果所有实测值都在范围内，自动选择合格
                if (measurements.length > 0 && !isNaN(minValue) && !isNaN(maxValue)) {
                    const allInRange = measurements.every(value => value >= minValue && value <= maxValue);
                    const radioGroup = row.querySelector(`input[name="size_${rowNumber}_check"]`);
                    
                    if (allInRange) {
                        row.querySelector(`input[name="size_${rowNumber}_check"][value="合格"]`).checked = true;
                    } else {
                        row.querySelector(`input[name="size_${rowNumber}_check"][value="不合格"]`).checked = true;
                    }
                }
            }

            // 为所有尺寸输入框添加事件监听
            document.querySelectorAll('.custom-row-height').forEach(row => {
                const inputs = row.querySelectorAll('input[type="text"]');
                inputs.forEach(input => {
                    input.addEventListener('input', function() {
                        // 延迟执行检查，等待用户输入完成
                        setTimeout(() => {
                            checkMeasurements(row);
                        }, 500);
                    });
                });
            });

            // 最终判定按钮处理
            document.querySelectorAll('input[name="final_judgment"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    const btn = document.getElementById('submit-btn');
                    btn.disabled = false; // 确保选择后按钮可用
                });
            });

            // 获取两个复选框元素
            const trialSampleCheckbox = document.getElementById('trial-sample');
            const bigSampleCheckbox = document.getElementById('big-sample');

            // 为试产样品复选框绑定事件
            trialSampleCheckbox?.addEventListener('change', function () {
                if (this.checked) {
                    bigSampleCheckbox.checked = false; // 如果选择试产样品，则取消大货样品的选择
                }
            });

            // 为大货样品复选框绑定事件
            bigSampleCheckbox?.addEventListener('change', function () {
                if (this.checked) {
                    trialSampleCheckbox.checked = false; // 如果选择大货样品，则取消试产样品的选择
                }
            });

            // 绑定提交按钮点击事件
            document.getElementById('submit-btn')?.addEventListener('click', async function (event) {
                event.preventDefault();
                const btn = this;
                btn.disabled = true; // 禁用按钮
                
                try {
                    // 基本表单验证
                    const materialNumber = document.getElementById('material-number').value;
                    const supplier = document.getElementById('supplier').value;
                    
                    if (!materialNumber) {
                        showFeedback('请填写物料料号', 'error');
                        btn.disabled = false;
                        return;
                    }
                    
                    if (!supplier) {
                        showFeedback('请填写供应商', 'error');
                        btn.disabled = false;
                        return;
                    }
                    
                    // 验证最终判定
                    const qualified = document.querySelector('input[name="final_judgment"]:checked');
                    if (!qualified) {
                        showFeedback('请完成最终判定选择', 'error');
                        btn.disabled = false;
                        return;
                    }
                    
                    // 检查所有显示图片的上传框是否都有临时ID
                    const imageBoxesWithImage = document.querySelectorAll('.image-upload-box.has-image');
                    let missingTempId = false;
                    
                    imageBoxesWithImage.forEach(box => {
                        const fileInput = box.querySelector('.image-input');
                        const previewImage = box.querySelector('.preview-image');
                        
                        // 检查是否有图片预览但没有temp-id
                        if (previewImage.style.display === 'block' && previewImage.src && 
                            !fileInput.hasAttribute('data-temp-id')) {
                            
                            missingTempId = true;
                            
                            // 尝试上传图片
                            if (fileInput.files && fileInput.files[0]) {
                                showFeedback('正在处理未上传的图片，请稍候...', 'info');
                                handleImageUpload(fileInput);
                            } else {
                                box.classList.remove('has-image');
                                previewImage.style.display = 'none';
                                box.querySelector('.upload-overlay').style.display = 'block';
                            }
                        }
                    });
                    
                    // 如果有图片未上传，暂停提交
                    if (missingTempId) {
                        showFeedback('有图片尚未完成上传，请重试', 'error');
                        btn.disabled = false;
                        return;
                    }
                    
                    // 收集所有图片的临时ID
                    const tempIds = [];
                    document.querySelectorAll('.image-input[data-temp-id]').forEach(input => {
                        const tempId = input.getAttribute('data-temp-id');
                        if (tempId) {
                            tempIds.push(tempId);
                        }
                    });
                    
                    // 获取表单数据
                    const form = document.getElementById('material-form');
                    const formData = new FormData(form);
                    
                    // 准备提交的数据
                    const formObject = {};
                    for (const [key, value] of formData.entries()) {
                        formObject[key] = value;
                    }

                    // 特殊处理功能检查5的测试项
                    const function5Tests = [];
                    document.querySelectorAll('input[name="function_5_tests[]"]:checked').forEach(checkbox => {
                        function5Tests.push(checkbox.value);
                    });
                    formObject['function_5_tests'] = function5Tests.join(',');
                    
                    // 添加图片临时ID
                    formObject.temp_ids = tempIds;
                    
                    // 收集样品状态复选框的值
                    const sampleStatus = [];
                    if (document.getElementById('trial-sample').checked) sampleStatus.push('试产样品');
                    if (document.getElementById('big-sample').checked) sampleStatus.push('大货样品');
                    if (document.getElementById('template').checked) sampleStatus.push('重制样板');
                    if (document.getElementById('other').checked) sampleStatus.push('其他'); // 仅添加"其他"选项

                    // 单独处理其他文本输入
                    const otherText = document.getElementById('other_textbox').value.trim();
                    formObject.sample_status = sampleStatus.join(','); // 仅传递选项名称
                    formObject.other_textbox = otherText; // 将输入文本单独保存到 other_textbox 字段
                    
                    // 提交表单数据
                    const response = await fetch('/Material_Sample_Confirmation_Form/submit_material_sample', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formObject)
                    });
                    
                    const result = await response.json();
                    
                    if (result.status === 'success') {
                        showFeedback(`提交成功！报告编码: ${result.report_code}`, 'success');
                        // 清除所有图片预览
                        document.querySelectorAll('.image-upload-box').forEach(box => {
                            const previewImage = box.querySelector('.preview-image');
                            const uploadOverlay = box.querySelector('.upload-overlay');
                            const deleteBtn = box.querySelector('.delete-btn');
                            const fileInput = box.querySelector('.image-input');
                            
                            // 清除图片预览
                            previewImage.src = '';
                            previewImage.style.display = 'none';
                            uploadOverlay.style.display = 'block';
                            deleteBtn.style.display = 'none';
                            box.classList.remove('has-image');
                            
                            // 清除文件输入和临时ID
                            fileInput.value = '';
                            fileInput.removeAttribute('data-temp-id');
                        });
                        
                        setTimeout(() => {
                            form.reset();
                            // 重新设置日期为今天
                            const now = new Date();
                            const offset = now.getTimezoneOffset() * 60000;
                            const today = new Date(now.getTime() - offset).toISOString().split('T')[0];
                            document.getElementById('date').value = today;
                            
                            // 强制刷新页面
                            window.location.reload(true);
                        }, 2000);
                    } else {
                        showFeedback(result.message || '提交失败', 'error');
                    }
            } catch (error) {
                    console.error('提交失败:', error);
                    showFeedback('提交失败，请检查网络或联系管理员', 'error');
                } finally {
                    btn.disabled = false;
                }
            });

            // 初始化所有上传按钮和复制按钮的悬停效果
            function initButtonHoverEffects() {
                // 初始化所有复制按钮
                document.querySelectorAll('.copy-button').forEach(button => {
                    // 添加悬停效果
                    button.addEventListener('mouseover', function() {
                        this.innerHTML = '📑 复制';
                        this.style.width = '70px';
                        this.style.paddingLeft = '8px';
                    });
                    
                    button.addEventListener('mouseout', function() {
                        this.innerHTML = '📑';
                        this.style.width = '28px';
                        this.style.paddingLeft = '4px';
                    });
                });
                
                // 初始化所有上传按钮
                document.querySelectorAll('.upload-button').forEach(button => {
                    // 添加悬停效果
                    button.addEventListener('mouseover', function() {
                        this.innerHTML = '📂 上传';
                        this.style.width = '70px';
                        this.style.paddingLeft = '8px';
                    });
                    
                    button.addEventListener('mouseout', function() {
                        this.innerHTML = '📂';
                        this.style.width = '28px';
                        this.style.paddingLeft = '4px';
                    });
                });
            }
            
            // 初始化按钮效果
            initButtonHoverEffects();
            
            // 每当添加新的问题点时，重新初始化按钮效果
            document.getElementById('add-question-btn')?.addEventListener('click', function() {
                // 延迟执行，确保DOM已更新
                setTimeout(initButtonHoverEffects, 100);
            });
        });

        // 图片上传处理函数
        async function handleImageUpload(fileInput) {
            if (!fileInput.files || !fileInput.files[0]) return;
            
            // 检查物料编码是否已填写
            const materialNumber = document.getElementById('material-number').value.trim();
            if (!materialNumber) {
                showFeedback('请先填写物料料号！', 'error');
                fileInput.value = ''; // 清空选择的文件
                return;
            }
            
            const box = fileInput.closest('.image-upload-box');
            const previewImage = box.querySelector('.preview-image');
            const uploadOverlay = box.querySelector('.upload-overlay');
            const deleteBtn = box.querySelector('.delete-btn');
            
            // 添加上传中状态
            box.classList.add('uploading');
            
            try {
                // 压缩图片
                const originalFile = fileInput.files[0];
                const originalSize = originalFile.size / 1024;
                
                // 使用Promise链式调用替代await
                compressImage(originalFile)
                    .then(compressedBlob => {
                const compressedSize = compressedBlob.size / 1024;
                
                // 使用压缩后的Blob创建新的File对象
                // 添加时间戳和随机数作为文件名前缀，避免文件名冲突
                const timestamp = new Date().getTime();
                const randomNum = Math.floor(Math.random() * 10000);
                const fileName = `${timestamp}_${randomNum}_${originalFile.name}`;
                
                const compressedFile = new File([compressedBlob], fileName, {
                    type: 'image/jpeg',
                    lastModified: Date.now()
                });
                
                // 用压缩后的文件替换原文件
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(compressedFile);
                fileInput.files = dataTransfer.files;
                
                // 获取表单数据
                const materialNumber = document.getElementById('material-number').value;
                const cellId = fileInput.getAttribute('data-question');
                const existingTempId = fileInput.getAttribute('data-temp-id');
                
                if (!materialNumber) {
                    showFeedback('请先填写物料料号', 'error');
                    box.classList.remove('uploading');
                            return Promise.reject(new Error('物料料号为空'));
                }
                
                // 检查是否有旧图片
                if (existingTempId) {
                    // 先删除旧图片
                            return fetch('/Material_Sample_Confirmation_Form/delete_image', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            temp_id: existingTempId,
                            material_number: materialNumber,
                            question_id: cellId
                        })
                            })
                            .then(response => {
                                // 即使删除失败也继续上传新图片
                                console.log('删除旧图片响应状态:', response.status);
                                
                                // 创建FormData对象，用于发送文件
                                const formData = new FormData();
                                formData.append('image', compressedFile);
                                formData.append('material_number', materialNumber);
                                formData.append('cell_id', cellId);
                                // 添加时间戳避免缓存问题
                                formData.append('timestamp', timestamp);
                                
                                // 上传图片到服务器
                                return fetch('/Material_Sample_Confirmation_Form/upload_image', {
                                    method: 'POST',
                                    body: formData
                                });
                            });
                        } else {
                // 创建FormData对象，用于发送文件
                        const formData = new FormData();
                formData.append('image', compressedFile);
                formData.append('material_number', materialNumber);
                formData.append('cell_id', cellId);
                // 添加时间戳避免缓存问题
                formData.append('timestamp', timestamp);
                
                // 上传图片到服务器
                            return fetch('/Material_Sample_Confirmation_Form/upload_image', {
                            method: 'POST',
                            body: formData
                            });
                        }
                })
                .then(response => {
                    if (!response.ok) {
                        // 获取错误详情
                        return response.json().then(errorData => {
                            throw new Error(errorData.message || errorData.error || `服务器错误: ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        console.log('图片上传成功:', data);
                        
                        // 将temp_id存储到DOM中，方便后续提交
                        fileInput.setAttribute('data-temp-id', data.temp_id);
                        
                        // 显示预览图片
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            previewImage.src = e.target.result;
                            previewImage.style.display = 'block';
                            uploadOverlay.style.display = 'none';
                            deleteBtn.style.display = 'block';
                            box.classList.add('has-image');
                            box.classList.remove('uploading');
                                
                                // 显示复制按钮
                                const copyButton = box.querySelector('.copy-button');
                                if (copyButton) {
                                    copyButton.style.display = 'block';
                                }
                                
                                // 为新上传的图片添加放大功能
                                addZoomFunctionality(previewImage);
                            
                            // 提示压缩结果
                                const originalSize = fileInput.files[0].size / 1024;
                                const compressedSize = fileInput.files[0].size / 1024;
                            if (originalSize > compressedSize && originalSize > 100) {
                                showFeedback(`图片已压缩并上传: ${Math.round(originalSize)}KB → ${Math.round(compressedSize)}KB`, 'success');
                } else {
                                showFeedback('图片已成功上传', 'success');
                            }
                        };
                            reader.readAsDataURL(fileInput.files[0]);
                    } else {
                        console.error('图片上传失败:', data);
                        showFeedback(data.message || '图片上传失败', 'error');
                        box.classList.remove('uploading');
                    }
                })
                .catch(error => {
                    console.error('图片上传错误:', error);
                    showFeedback(`图片上传失败: ${error.message || '请检查网络连接'}`, 'error');
                    box.classList.remove('uploading');
                });
            } catch (error) {
                console.error('图片处理失败:', error);
                showFeedback('图片处理失败', 'error');
                box.classList.remove('uploading');
            }
        }

        // 添加压缩图片功能
        async function compressImage(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        let width = img.width;
                        let height = img.height;

                        // 按比例缩小尺寸
                        if (width > 1600 || height > 1600) {
                            const ratio = Math.min(1600 / width, 1600 / height);
                            width = Math.floor(width * ratio);
                            height = Math.floor(height * ratio);
                        }

                        canvas.width = width;
                        canvas.height = height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0, width, height);

                        // 动态调整质量
                        let quality = 0.8;
                        if (file.size > 1024 * 1024) {
                            // 如果原图大于1MB，降低质量
                            quality = 0.7;
                        }
                        
                        canvas.toBlob((blob) => {
                            if (blob.size > 1024 * 1024) {
                                // 如果压缩后仍大于1MB，进一步降低质量
                                quality = Math.min(0.6, (1024 * 1024) / blob.size);
                                canvas.toBlob(resolve, 'image/jpeg', quality);
                            } else {
                                resolve(blob);
                            }
                        }, 'image/jpeg', quality);
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
        }

        // 显示反馈消息的函数
        function showFeedback(message, type = 'info') {
            const feedbackEl = document.getElementById('feedback-message');
            feedbackEl.textContent = message;
            
            // 添加样式基于反馈类型
            feedbackEl.style.backgroundColor = type === 'success' ? 'rgba(40, 167, 69, 0.9)' : 
                                              type === 'error' ? 'rgba(220, 53, 69, 0.9)' : 
                                              'rgba(0, 0, 0, 0.7)';
            
            // 显示反馈
            feedbackEl.classList.add('show');
            
            // 2秒后隐藏
            setTimeout(() => {
                feedbackEl.classList.remove('show');
            }, 2000);
        }

        // 尺寸行增删功能
        document.addEventListener('DOMContentLoaded', function() {
            // 获取按钮和行元素
            const addBtn = document.getElementById('add-size-row');
            const removeBtn = document.getElementById('remove-size-row');
            const controlsRow = document.getElementById('size-row-controls');
            const dynamicRows = document.querySelectorAll('.dynamic-size-row');
            const sizeSectionCell = document.getElementById('size-section-cell');
            
            // 当前显示的动态行数量
            let visibleRowsCount = 0;
            
            // 更新按钮状态和位置
            function updateButtonState() {
                // 禁用或启用按钮
                removeBtn.disabled = visibleRowsCount === 0;
                addBtn.disabled = visibleRowsCount === 27; // 最多显示27个动态行（4-30行）
                
                // 移动控制按钮到最后一个可见尺寸行之后
                const lastVisibleRow = visibleRowsCount === 0 
                    ? document.querySelector('.custom-row-height:nth-child(5)') // 第3行后面
                    : document.getElementById(`size-row-${3 + visibleRowsCount}`);
                
                if (lastVisibleRow && lastVisibleRow.nextSibling !== controlsRow) {
                    lastVisibleRow.after(controlsRow);
                }
                
                // 更新按钮样式
                removeBtn.style.opacity = visibleRowsCount === 0 ? '0.5' : '1';
                addBtn.style.opacity = visibleRowsCount === 27 ? '0.5' : '1';
                
                // 更新"一、尺寸"单元格的rowspan属性
                // 基础的rowspan值是5（标题行+表头行+3个基础数据行）
                // 然后加上显示的动态行数量和控制按钮行（1行）
                sizeSectionCell.setAttribute('rowspan', 5 + visibleRowsCount + 1);
            }
            
            // 添加尺寸行
            addBtn.addEventListener('click', function() {
                if (visibleRowsCount < 27) {
                    // 显示下一个隐藏的行
                    const nextRowIndex = 4 + visibleRowsCount;
                    const nextRow = document.getElementById(`size-row-${nextRowIndex}`);
                    if (nextRow) {
                        nextRow.style.display = '';
                        visibleRowsCount++;
                        updateButtonState();
                    }
                }
            });
            
            // 删除尺寸行
            removeBtn.addEventListener('click', function() {
                if (visibleRowsCount > 0) {
                    // 隐藏最后一个可见行
                    const lastRowIndex = 3 + visibleRowsCount;
                    const lastRow = document.getElementById(`size-row-${lastRowIndex}`);
                    if (lastRow) {
                        lastRow.style.display = 'none';
                        
                        // 清空该行的所有输入
                        const inputs = lastRow.querySelectorAll('input[type="text"], textarea');
                        inputs.forEach(input => input.value = '');
                        
                        // 清空单选框选择
                        const radios = lastRow.querySelectorAll('input[type="radio"]');
                        radios.forEach(radio => radio.checked = false);
                        
                        visibleRowsCount--;
                        updateButtonState();
                    }
                }
            });
            
            // 初始化按钮状态
            updateButtonState();
        });

        // 全局变量，用于控制按钮状态
        let isAddingRow = false;
        let isRemovingRow = false;

        // 添加尺寸行
        function addSizeRow() {
            if (isAddingRow) return;  // 防止重复点击
            
            const currentRows = document.querySelectorAll('.size-row').length;
            const addButton = document.getElementById('addSizeRowBtn');
            
            // 显示加载状态
            addButton.disabled = true;
            addButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';
            isAddingRow = true;
            
            // 发送请求
            fetch('/Material_Sample_Confirmation_Form/add_size_row', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `current_rows=${currentRows}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // 添加新行
                    const sizeTable = document.getElementById('sizeTable');
                    const tbody = sizeTable.querySelector('tbody');
                    tbody.insertAdjacentHTML('beforeend', data.html);
                    
                    // 更新行号
                    updateRowNumbers();
                    
                    // 添加动画效果
                    const newRow = tbody.lastElementChild;
                    newRow.style.opacity = '0';
                    setTimeout(() => {
                        newRow.style.transition = 'opacity 0.3s';
                        newRow.style.opacity = '1';
                    }, 50);
                } else {
                    showError(data.message || '添加行失败');
                }
            })
            .catch(error => {
                showError('添加行时发生错误: ' + error);
            })
            .finally(() => {
                // 恢复按钮状态
                setTimeout(() => {
                    addButton.disabled = false;
                    addButton.innerHTML = '<i class="fas fa-plus"></i> 添加尺寸行';
                    isAddingRow = false;
                }, 300);  // 额外延迟，确保UI完全响应
            });
        }

        // 删除尺寸行
        function removeSizeRow(button) {
            if (isRemovingRow) return;  // 防止重复点击
            
            const row = button.closest('tr');
            const rowNumber = row.dataset.rowNumber;
            
            // 显示加载状态
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            isRemovingRow = true;
            
            // 发送请求
            fetch('/Material_Sample_Confirmation_Form/remove_size_row', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `row_number=${rowNumber}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // 添加删除动画
                    row.style.transition = 'opacity 0.3s, transform 0.3s';
                    row.style.opacity = '0';
                    row.style.transform = 'translateX(-100%)';
                    
                    // 延迟删除元素
                    setTimeout(() => {
                        row.remove();
                        updateRowNumbers();
                    }, 300);
                } else {
                    showError(data.message || '删除行失败');
                }
            })
            .catch(error => {
                showError('删除行时发生错误: ' + error);
            })
            .finally(() => {
                // 恢复按钮状态
                setTimeout(() => {
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-trash"></i>';
                    isRemovingRow = false;
                }, 300);
            });
        }

        // 更新行号
        function updateRowNumbers() {
            const rows = document.querySelectorAll('.size-row');
            rows.forEach((row, index) => {
                row.dataset.rowNumber = index + 1;
                const numberCell = row.querySelector('.row-number');
                if (numberCell) {
                    numberCell.textContent = index + 1;
                }
            });
        }

        // 显示错误消息
        function showError(message) {
            // 这里可以添加错误提示的UI实现
            alert(message);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加事件监听器
            const addButton = document.getElementById('addSizeRowBtn');
            if (addButton) {
                addButton.addEventListener('click', addSizeRow);
            }
            
            // 初始化行号
            updateRowNumbers();

            // 问题点计数器
            let questionCount = 6;
            
            // 获取问题点容器
            const questionContainer = Array.from(document.querySelectorAll('td')).find(td => td.textContent.includes('四、补充问题点/题点')).closest('tr').nextElementSibling;
            
            // 添加问题点按钮点击事件
            document.getElementById('add-question-btn').addEventListener('click', function() {
                if (questionCount >= 18) {
                    alert('已达到最大问题点数量限制(18个)');
                    return;
                }
                
                // 获取增加问题点按钮所在行
                const addButtonRow = document.querySelector('#add-question-btn').closest('tr');
                
                // 创建新的问题点图片行
                const newQuestionRow = document.createElement('tr');
                newQuestionRow.innerHTML = '<td colspan="4" class="image-upload-cell">' +
                    '<div class="image-upload-box">' +
                    '<input type="file" class="image-input" data-question="' + (questionCount + 1) + '" accept="image/*" />' +
                    '<div class="upload-overlay">点击上传或粘贴截图<span class="upload-shortcut">支持拖放图片或粘贴</span></div>' +
                    '<img class="preview-image" />' +
                    '<button class="delete-btn" data-question="' + (questionCount + 1) + '" type="button">×</button>' +
                    '<button class="paste-button" data-question="' + (questionCount + 1) + '" type="button">📋</button>' +
                    '<button class="copy-button" type="button">📑</button>' +
                    '<button class="upload-button" type="button">📂</button>' +
                    '</div></td>' +
                    '<td colspan="5" class="image-upload-cell">' +
                    '<div class="image-upload-box">' +
                    '<input type="file" class="image-input" data-question="' + (questionCount + 2) + '" accept="image/*" />' +
                    '<div class="upload-overlay">点击上传或粘贴截图<span class="upload-shortcut">支持拖放图片或粘贴</span></div>' +
                    '<img class="preview-image" />' +
                    '<button class="delete-btn" data-question="' + (questionCount + 2) + '" type="button">×</button>' +
                    '<button class="paste-button" data-question="' + (questionCount + 2) + '" type="button">📋</button>' +
                    '<button class="copy-button" type="button">📑</button>' +
                    '<button class="upload-button" type="button">📂</button>' +
                    '</div></td>' +
                    '<td colspan="5" class="image-upload-cell">' +
                    '<div class="image-upload-box">' +
                    '<input type="file" class="image-input" data-question="' + (questionCount + 3) + '" accept="image/*" />' +
                    '<div class="upload-overlay">点击上传或粘贴截图<span class="upload-shortcut">支持拖放图片或粘贴</span></div>' +
                    '<img class="preview-image" />' +
                    '<button class="delete-btn" data-question="' + (questionCount + 3) + '" type="button">×</button>' +
                    '<button class="paste-button" data-question="' + (questionCount + 3) + '" type="button">📋</button>' +
                    '<button class="copy-button" type="button">📑</button>' +
                    '<button class="upload-button" type="button">📂</button>' +
                    '</div></td>';
                
                // 创建新的问题描述行
                const newDescriptionRow = document.createElement('tr');
                newDescriptionRow.innerHTML = '<td colspan="4" style="height: 20px;">' +
                    '<textarea id="question_' + (questionCount + 1) + '" name="question_' + (questionCount + 1) + '"></textarea>' +
                    '</td>' +
                    '<td colspan="5" style="height: 20px;">' +
                    '<textarea id="question_' + (questionCount + 2) + '" name="question_' + (questionCount + 2) + '"></textarea>' +
                    '</td>' +
                    '<td colspan="5" style="height: 20px;">' +
                    '<textarea id="question_' + (questionCount + 3) + '" name="question_' + (questionCount + 3) + '"></textarea>' +
                    '</td>';
                
                // 在增加问题点按钮行之前插入新行
                addButtonRow.parentNode.insertBefore(newQuestionRow, addButtonRow);
                addButtonRow.parentNode.insertBefore(newDescriptionRow, addButtonRow);
                
                // 初始化新添加的图片上传框
                initializeImageUploadBoxes(newQuestionRow);
                
                questionCount += 3;
                
                // 为新添加的问题点7-18重新绑定事件
                initDynamicQuestionEvents();
            });
            
            // 删除问题点按钮点击事件
            document.getElementById('remove-question-btn').addEventListener('click', function() {
                if (questionCount <= 6) {
                    alert('已达到最小问题点数量限制(6个)');
                    return;
                }
                
                // 获取增加问题点按钮所在行
                const addButtonRow = document.querySelector('#add-question-btn').closest('tr');
                
                // 删除增加问题点按钮行前面的两行（图片行和描述行）
                const lastDescriptionRow = addButtonRow.previousElementSibling;
                const lastImageRow = lastDescriptionRow.previousElementSibling;
                
                lastImageRow.remove();
                lastDescriptionRow.remove();
                
                questionCount -= 3;
            });
            
            // 初始化图片上传框
            function initializeImageUploadBoxes(container) {
                const imageBoxes = container.querySelectorAll('.image-upload-box');
                imageBoxes.forEach(function(box) {
                    // 防止重复绑定事件
                    if (box.hasAttribute('data-box-initialized')) return;
                    box.setAttribute('data-box-initialized', 'true');
                    
                    const fileInput = box.querySelector('.image-input');
                    const previewImage = box.querySelector('.preview-image');
                    const uploadOverlay = box.querySelector('.upload-overlay');
                    const deleteBtn = box.querySelector('.delete-btn');
                    const pasteButton = box.querySelector('.paste-button');
                    
                    // 文件选择处理
                    fileInput.addEventListener('change', function(e) {
                        // 检查物料编码是否已填写
                        const materialNumber = document.getElementById('material-number').value.trim();
                        if (!materialNumber) {
                            showFeedback('请先填写物料料号！', 'error');
                            // 清空选择的文件
                            fileInput.value = '';
                            return;
                        }
                        
                        const file = e.target.files[0];
                        if (file) {
                            // 检查是否有旧图片需要先删除
                            const tempId = this.getAttribute('data-temp-id');
                            const questionNumber = parseInt(this.getAttribute('data-question'));
                            
                            // 对于问题点7-18，添加确认对话框
                            if (tempId && questionNumber >= 7 && questionNumber <= 18) {
                                if (confirm('确定要删除旧图片并上传新图片吗？')) {
                                    // 删除旧图片
                                    fetch('/Material_Sample_Confirmation_Form/delete_image', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            question_id: questionNumber,
                                            material_number: materialNumber
                                        })
                                    })
                                    .then(() => {
                                        // 删除成功后上传新图片
                                        handleImageUpload(this);
                                        // 显示预览
                                        showImagePreview(file);
                                    })
                                    .catch(error => {
                                        console.error('删除旧图片错误:', error);
                                        showFeedback('删除旧图片失败，请重试', 'error');
                                    });
                                } else {
                                    this.value = ''; // 取消操作时清空文件选择
                                    return;
                                }
                            } else {
                                // 显示预览并上传
                                showImagePreview(file);
                                handleImageUpload(this);
                            }
                            
                            // 显示图片预览的函数
                            function showImagePreview(file) {
                                const reader = new FileReader();
                                reader.onload = function(e) {
                                    previewImage.src = e.target.result;
                                    previewImage.style.display = 'block';
                                    uploadOverlay.style.display = 'none';
                                    box.classList.add('has-image');
                                    
                                    // 显示复制按钮
                                    const copyButton = box.querySelector('.copy-button');
                                    if (copyButton) {
                                        copyButton.style.display = 'block';
                                    }
                                    
                                    // 为新上传的图片添加放大功能
                                    addZoomFunctionality(previewImage);
                                };
                                reader.readAsDataURL(file);
                            }
                        }
                    });
                    
                    // 为图片上传框添加点击事件，触发文件选择
                    box.addEventListener('click', function(e) {
                        // 如果点击的是删除按钮或粘贴按钮或复制按钮或上传按钮，不触发文件选择
                        if (e.target.classList.contains('delete-btn') || 
                            e.target.classList.contains('paste-button') ||
                            e.target.classList.contains('copy-button') ||
                            e.target.classList.contains('upload-button')) {
                            return;
                        }
                        
                        // 检查物料编码是否已填写
                        const materialNumber = document.getElementById('material-number').value.trim();
                        if (!materialNumber) {
                            showFeedback('请先填写物料料号！', 'error');
                            return;
                        }
                        
                        // 更新最后点击的上传框
                        window.lastClickedBox = this;
                        
                        // 触发文件选择
                        if (fileInput) {
                            fileInput.click();
                        }
                    });
                    
                    // 添加拖放功能
                    box.addEventListener('dragover', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        this.classList.add('drag-over');
                    });
                    
                    box.addEventListener('dragleave', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        this.classList.remove('drag-over');
                    });
                    
                    box.addEventListener('drop', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        this.classList.remove('drag-over');
                        
                        // 检查物料编码是否已填写
                        const materialNumber = document.getElementById('material-number').value.trim();
                        if (!materialNumber) {
                            showFeedback('请先填写物料料号！', 'error');
                            return;
                        }
                        
                        const dt = e.dataTransfer;
                        const files = dt.files;
                        
                        if (files.length) {
                            fileInput.files = files;
                            
                            // 触发change事件
                            const event = new Event('change', { bubbles: true });
                            fileInput.dispatchEvent(event);
                            
                            showFeedback('图片已成功拖放上传', 'success');
                        }
                    });
                    
                    // 删除按钮处理
                    deleteBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        // 获取物料料号
                        const materialNumber = document.getElementById('material-number').value;
                        // 获取问题点编号
                        const questionNumber = parseInt(fileInput.getAttribute('data-question'));
                        
                        // 检查是否有临时ID（已上传到服务器的图片）
                        const tempId = fileInput.getAttribute('data-temp-id');
                        
                        // 对于问题点7-18，添加确认对话框
                        if (questionNumber >= 7 && questionNumber <= 18) {
                            if (!confirm('确定要删除图片吗？')) {
                                return; // 用户取消删除操作
                            }
                        }
                        
                        if (tempId) {
                            // 从服务器删除图片
                            fetch('/Material_Sample_Confirmation_Form/delete_image', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    question_id: questionNumber,
                                    material_number: materialNumber
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // 清除图片
                                    clearImageBox();
                                } else {
                                    showFeedback(data.message || '图片删除失败', 'error');
                                }
                            })
                            .catch(error => {
                                console.error('删除图片错误:', error);
                                showFeedback('图片删除失败，请检查网络连接', 'error');
                            });
                        } else {
                            // 直接清除本地预览的图片
                            clearImageBox();
                        }
                        
                        function clearImageBox() {
                            previewImage.src = '';
                            previewImage.style.display = 'none';
                            uploadOverlay.style.display = 'block';
                            deleteBtn.style.display = 'none';
                            box.classList.remove('has-image');
                            deleteBtn.style.display = 'none';
                            
                            // 不再隐藏复制、粘贴和上传按钮
                            // const copyButton = box.querySelector('.copy-button');
                            // if (copyButton) {
                            //     copyButton.style.display = 'none';
                            // }
                        }
                    });
                    
                    // 粘贴按钮处理
                    if (pasteButton) {
                        pasteButton.addEventListener('mouseover', function() {
                            this.textContent = '📋 粘贴';
                            this.style.width = '70px';
                            this.style.paddingLeft = '8px';
                        });
                        
                        pasteButton.addEventListener('mouseout', function() {
                            this.textContent = '📋';
                            this.style.width = '28px';
                            this.style.paddingLeft = '4px';
                        });
                    }
                    
                    // 添加复制按钮
                    if (!box.querySelector('.copy-button')) {
                        const copyButton = document.createElement('button');
                        copyButton.className = 'copy-button';
                        copyButton.innerHTML = '📑';
                        copyButton.type = 'button';
                        
                        // 默认隐藏复制按钮，只在有图片时显示
                        copyButton.style.display = 'none';
                        
                        // 添加悬停效果
                        copyButton.addEventListener('mouseover', function() {
                            this.innerHTML = '📑 复制';
                            this.style.width = '70px';
                            this.style.paddingLeft = '8px';
                        });
                        
                        copyButton.addEventListener('mouseout', function() {
                            this.innerHTML = '📑';
                            this.style.width = '28px';
                            this.style.paddingLeft = '4px';
                        });
                        
                        copyButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            
                            // 获取预览图片
                            const previewImage = box.querySelector('.preview-image');
                            if (!previewImage || !previewImage.src || previewImage.src === window.location.href) {
                                showFeedback('没有图片可复制', 'error');
                                return;
                            }
                            
                            // 复制图片到剪贴板
                            copyImageToClipboard(previewImage);
                        });
                        
                        box.appendChild(copyButton);
                    }
                    
                    // 添加上传按钮
                    if (!box.querySelector('.upload-button')) {
                        const uploadButton = document.createElement('button');
                        uploadButton.className = 'upload-button';
                        uploadButton.innerHTML = '📂';
                        uploadButton.type = 'button';
                        
                        // 添加悬停效果
                        uploadButton.addEventListener('mouseover', function() {
                            this.innerHTML = '📂 上传';
                            this.style.width = '70px';
                            this.style.paddingLeft = '8px';
                        });
                        
                        uploadButton.addEventListener('mouseout', function() {
                            this.innerHTML = '📂';
                            this.style.width = '28px';
                            this.style.paddingLeft = '4px';
                        });
                        
                        uploadButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            
                            // 直接检查物料编码，而不是调用validateMaterialNumber函数
                            const materialNumber = document.getElementById('material-number').value.trim();
                            if (!materialNumber) {
                                showFeedback('请先填写物料料号！', 'error');
                                return;
                            }
                            
                            window.lastClickedBox = box;
                            
                            // 点击时触发文件选择框
                            if (fileInput) {
                                fileInput.click();
                            }
                        });
                        
                        box.appendChild(uploadButton);
                    }
                    
                    // 如果已有图片，初始化放大功能
                    if (previewImage.src && previewImage.src !== window.location.href) {
                        addZoomFunctionality(previewImage);
                    }
                });
            }
        });

        // 为图片添加放大功能
        function addZoomFunctionality(img) {
            // 获取模态框元素
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const closeBtn = modal.querySelector('.close');
            const tooltip = document.getElementById('image-tooltip');
            const zoomIn = document.getElementById('zoom-in');
            const zoomOut = document.getElementById('zoom-out');
            const resetView = document.getElementById('reset-view');
            const loader = modal.querySelector('.modal-loader');
            
            if (!modal || !modalImg || !closeBtn) {
                console.error('找不到模态框元素，无法添加图片放大功能');
                return;
            }
            
            // 缩放和移动相关变量
            let scale = 1;
            let translateX = 0;
            let translateY = 0;
            let isDragging = false;
            let startX, startY;
            let lastTapTime = 0;
            
            // 显示操作提示
            function showTooltip(text, x, y, duration = 1500) {
                tooltip.textContent = text;
                tooltip.style.left = `${x}px`;
                tooltip.style.top = `${y}px`;
                tooltip.style.opacity = '1';
                
                setTimeout(() => {
                    tooltip.style.opacity = '0';
                }, duration);
            }
            
            // 点击图片时显示模态框
            img.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 显示加载状态
                modal.style.display = 'block';
                loader.style.display = 'block';
                
                // 设置图片源
                modalImg.src = this.src;
                
                // 重置变换
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateTransform(true);
                
                // 动画显示模态框
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10);
                
                // 图片加载完成后
                modalImg.onload = function() {
                    loader.style.display = 'none';
                    
                    setTimeout(() => {
                        modalImg.classList.add('show');
                        
                        // 显示操作提示
                        showTooltip('滚轮或触摸缩放，拖拽移动，双击重置', 
                                 window.innerWidth / 2, 
                                 window.innerHeight - 100, 
                                 3000);
                    }, 50);
                };
                
                // 防止点击事件冒泡
                return false;
            });
            
            // 更新图片变换
            function updateTransform(isInitial = false) {
                const transform = `
                    translate(-50%, -50%)
                    translate(${translateX}px, ${translateY}px)
                    scale(${scale})
                `;
                
                if (isInitial) {
                    modalImg.style.transform = transform;
                } else {
                    modalImg.style.transition = 'none';
                    modalImg.style.transform = transform;
                    // 强制重排
                    void modalImg.offsetHeight;
                    modalImg.style.transition = 'transform 0.05s ease';
                }
            }
            
            // 计算动态缩放步长
            function calculateZoomStep(currentScale) {
                // 在不同缩放级别使用不同的步长
                if (currentScale < 0.5) return 0.1;
                if (currentScale < 1) return 0.15;
                if (currentScale < 2) return 0.25;
                if (currentScale < 5) return 0.5;
                return 1;
            }
            
            // 缩放功能
            function zoomImage(direction, fixedScale = null) {
                const zoomStep = fixedScale || calculateZoomStep(scale);
                const oldScale = scale;
                
                if (direction === 'in') {
                    scale = Math.min(10, scale + zoomStep);
                } else {
                    scale = Math.max(0.1, scale - zoomStep);
                }
                
                // 防止过小缩放
                if (scale < 0.1) scale = 0.1;
                
                // 显示当前缩放比例
                showTooltip(`缩放: ${Math.round(scale * 100)}%`, 
                          window.innerWidth / 2,
                          window.innerHeight / 2);
                
                updateTransform();
            }
            
            // 关闭模态框
            function closeModal() {
                modal.classList.remove('show');
                modalImg.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                    translateX = 0;
                    translateY = 0;
                    scale = 1;
                }, 300);
            }
            
            // 添加关闭按钮点击事件
            closeBtn.addEventListener('click', closeModal);
            
            // 点击模态框背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
            
            // 滚轮缩放
            modal.addEventListener('wheel', function(e) {
                e.preventDefault();
                zoomImage(e.deltaY < 0 ? 'in' : 'out');
            }, { passive: false });
            
            // 控制按钮事件
            zoomIn.addEventListener('click', () => zoomImage('in'));
            zoomOut.addEventListener('click', () => zoomImage('out'));
            resetView.addEventListener('click', () => {
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateTransform();
                showTooltip('视图已重置', window.innerWidth / 2, window.innerHeight / 2);
            });
            
            // 拖拽功能
            modalImg.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);
            
            function startDrag(e) {
                if (e.button === 0) { // 左键点击
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    modal.classList.add('grabbing');
                    e.preventDefault();
                    
                    // 检测双击
                    const now = new Date().getTime();
                    const timeDiff = now - lastTapTime;
                    if (timeDiff < 300 && timeDiff > 0) {
                        // 双击重置
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        updateTransform();
                        showTooltip('视图已重置', e.clientX, e.clientY);
                    }
                    lastTapTime = now;
                }
            }
            
            function drag(e) {
                if (!isDragging) return;
                
                const dx = e.clientX - startX;
                const dy = e.clientY - startY;
                startX = e.clientX;
                startY = e.clientY;
                
                // 动态调整拖动灵敏度
                const speedFactor = scale > 1 ? 1 : Math.sqrt(scale);
                translateX += dx / speedFactor;
                translateY += dy / speedFactor;
                
                updateTransform();
            }
            
            function endDrag() {
                if (isDragging) {
                    isDragging = false;
                    modal.classList.remove('grabbing');
                    modal.style.cursor = 'zoom-in';
                }
            }
            
            // 键盘控制
            document.addEventListener('keydown', function(e) {
                if (modal.style.display !== 'block') return;
                
                switch (e.key) {
                    case 'Escape':
                        closeModal();
                        break;
                    case 'ArrowLeft':
                        translateX += 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowRight':
                        translateX -= 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowUp':
                        translateY += 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowDown':
                        translateY -= 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case '+':
                    case '=':
                        zoomImage('in', 0.25);
                        e.preventDefault();
                        break;
                    case '-':
                    case '_':
                        zoomImage('out', 0.25);
                        e.preventDefault();
                        break;
                    case '0':
                    case 'r':
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        updateTransform();
                        showTooltip('视图已重置', window.innerWidth / 2, window.innerHeight / 2);
                        e.preventDefault();
                        break;
                }
            });
            
            // 添加触摸支持
            let lastTouchDistance = 0;
            
            modalImg.addEventListener('touchstart', function(e) {
                if (e.touches.length === 1) {
                    // 单指触摸开始拖动
                    isDragging = true;
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                } else if (e.touches.length === 2) {
                    // 双指触摸开始缩放
                    const dx = e.touches[0].clientX - e.touches[1].clientX;
                    const dy = e.touches[0].clientY - e.touches[1].clientY;
                    lastTouchDistance = Math.sqrt(dx * dx + dy * dy);
                }
                e.preventDefault(); // 阻止默认行为
            });
            
            modalImg.addEventListener('touchmove', function(e) {
                e.preventDefault(); // 阻止滚动
                
                if (e.touches.length === 1 && isDragging) {
                    // 单指拖动
                    const dx = e.touches[0].clientX - startX;
                    const dy = e.touches[0].clientY - startY;
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                    
                    const speedFactor = Math.sqrt(scale);
                    
                    translateX += dx / speedFactor;
                    translateY += dy / speedFactor;
                    
                    updateTransform();
                } 
                else if (e.touches.length === 2) {
                    // 双指缩放
                    const dx = e.touches[0].clientX - e.touches[1].clientX;
                    const dy = e.touches[0].clientY - e.touches[1].clientY;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    // 计算缩放比例
                    if (lastTouchDistance > 0) {
                        const scaleFactor = distance / lastTouchDistance;
                        const newScale = Math.max(0.5, Math.min(5, scale * scaleFactor));
                        
                        scale = newScale;
                        updateTransform();
                    }
                    
                    lastTouchDistance = distance;
                }
            });
            
            modalImg.addEventListener('touchend', function(e) {
                isDragging = false;
                if (e.touches.length < 2) {
                    lastTouchDistance = 0;
                }
            });
            
            // 双击重置缩放和位置
            modalImg.addEventListener('dblclick', function() {
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateTransform();
                showTooltip('视图已重置', window.innerWidth / 2, window.innerHeight / 2);
            });
        }
        
        // 复制图片到剪贴板函数
        function copyImageToClipboard(img) {
            // 创建一个canvas元素
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置canvas尺寸与图片相同
            canvas.width = img.naturalWidth || img.width;
            canvas.height = img.naturalHeight || img.height;
            
            // 将图片绘制到canvas上
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            
            // 尝试从canvas获取图片数据并复制到剪贴板
            try {
                canvas.toBlob(function(blob) {
                    // 创建ClipboardItem对象
                    const data = [new ClipboardItem({ 'image/png': blob })];
                    
                    // 写入剪贴板
                    navigator.clipboard.write(data)
                        .then(() => {
                            showFeedback('图片已复制到剪贴板', 'success');
                        })
                        .catch(err => {
                            console.error('复制到剪贴板失败:', err);
                            showFeedback('复制到剪贴板失败，请尝试其他方法', 'error');
                            
                            // 备用方法：打开图片在新窗口，让用户手动复制
                            const newWindow = window.open();
                            newWindow.document.write('<img src="' + canvas.toDataURL('image/png') + '" alt="复制此图片">');
                            newWindow.document.write('<p>请右键点击图片并选择"复制图片"</p>');
                        });
                }, 'image/png');
            } catch (err) {
                console.error('创建图片Blob失败:', err);
                showFeedback('复制图片失败，浏览器可能不支持此功能', 'error');
            }
        }

        // 初始化所有图片的放大功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有已有的图片添加放大功能
            document.querySelectorAll('.preview-image').forEach(img => {
                if (img.src && img.src !== window.location.href) {
                    addZoomFunctionality(img);
                }
            });
            
            // 初始化动态问题点的事件
            initDynamicQuestionEvents();
        });
        
        // 专门为问题点7-18绑定事件的函数
        function initDynamicQuestionEvents() {
            console.log('初始化问题点7-18事件');
            // 获取问题点7-18对应的box
            const dynamicQuestionBoxes = document.querySelectorAll('.image-upload-box');
            dynamicQuestionBoxes.forEach(box => {
                const fileInput = box.querySelector('.image-input');
                // 仅处理问题点7-18
                if (!fileInput || !fileInput.getAttribute('data-question')) return;
                
                const questionNum = parseInt(fileInput.getAttribute('data-question'));
                if (questionNum < 7) return; // 移除上限检查，处理所有7以上的问题点
                
                console.log('处理问题点', questionNum);
                
                // 防止重复绑定事件的标记 - 与initializeImageUploadBoxes保持一致
                if (box.hasAttribute('data-box-initialized')) {
                    // 即使box已初始化，也确保所有按钮有事件绑定
                    const uploadBtn = box.querySelector('.upload-button');
                    const copyBtn = box.querySelector('.copy-button');
                    const pasteBtn = box.querySelector('.paste-button');
                    
                    if (uploadBtn && !uploadBtn.hasAttribute('data-event-bound')) {
                        console.log('为问题点', questionNum, '绑定上传按钮事件');
                        bindUploadButtonEvents(uploadBtn, fileInput);
                    }
                    
                    if (copyBtn && !copyBtn.hasAttribute('data-event-bound')) {
                        console.log('为问题点', questionNum, '绑定复制按钮事件');
                        bindCopyButtonEvents(copyBtn, box);
                    }
                    
                    if (pasteBtn && !pasteBtn.hasAttribute('data-event-bound')) {
                        console.log('为问题点', questionNum, '绑定粘贴按钮事件');
                        bindPasteButtonEvents(pasteBtn, box, fileInput);
                    }
                    
                    return;
                }
                
                console.log('初始化问题点', questionNum, '的所有事件');
                box.setAttribute('data-box-initialized', 'true');
                
                // 5. 上传按钮事件 - 直接绑定，不依赖于其他初始化
                const uploadBtn = box.querySelector('.upload-button');
                if (uploadBtn) {
                    console.log('为问题点', questionNum, '绑定上传按钮事件');
                    // 添加悬停效果
                    uploadBtn.addEventListener('mouseover', function() {
                        this.innerHTML = '📂 上传';
                        this.style.width = '70px';
                        this.style.paddingLeft = '8px';
                    });
                    
                    uploadBtn.addEventListener('mouseout', function() {
                        this.innerHTML = '📂';
                        this.style.width = '28px';
                        this.style.paddingLeft = '4px';
                    });
                    
                    // 确保上传按钮有正确的事件处理
                    uploadBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        console.log('问题点', questionNum, '的上传按钮被点击');
                        
                        // 检查物料编码
                        const materialNumber = document.getElementById('material-number').value.trim();
                        if (!materialNumber) {
                            showFeedback('请先填写物料料号！', 'error');
                            return;
                        }
                        
                        // 设置当前活动框
                        window.lastClickedBox = box;
                        
                        // 直接触发文件选择
                        console.log('触发问题点', questionNum, '的文件选择');
                        fileInput.click();
                    });
                    
                    // 标记已绑定事件
                    uploadBtn.setAttribute('data-event-bound', 'true');
                }
                
                // 继续其他事件绑定...
                // 1. 上传框点击事件
                box.addEventListener('click', function(e) {
                    // 如果点击的是按钮，不触发文件选择
                    if (e.target.tagName === 'BUTTON') return;
                    
                    // 检查物料编码
                    const materialNumber = document.getElementById('material-number').value.trim();
                    if (!materialNumber) {
                        showFeedback('请先填写物料料号！', 'error');
                        return;
                    }
                    
                    // 更新最后点击的上传框
                    window.lastClickedBox = this;
                    
                    // 触发文件选择
                    if (fileInput) {
                        fileInput.click();
                    }
                });
                
                // 2. 文件输入变化事件
                fileInput.addEventListener('change', function() {
                    // 检查物料编码
                    const materialNumber = document.getElementById('material-number').value.trim();
                    if (!materialNumber) {
                        showFeedback('请先填写物料料号！', 'error');
                        this.value = '';
                        return;
                    }
                    
                    if (this.files && this.files[0]) {
                        const file = this.files[0];
                        handleImageUpload(this);
                    }
                });
                
                // 3. 粘贴按钮事件
                const pasteBtn = box.querySelector('.paste-button');
                if (pasteBtn) {
                    bindPasteButtonEvents(pasteBtn, box, fileInput);
                }
                
                // 4. 复制按钮事件
                const copyBtn = box.querySelector('.copy-button');
                if (copyBtn) {
                    bindCopyButtonEvents(copyBtn, box);
                }
                
                // 6. 拖放功能
                box.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.add('drag-over');
                });
                
                box.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.remove('drag-over');
                });
                
                box.addEventListener('drop', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.remove('drag-over');
                    
                    // 检查物料编码
                    const materialNumber = document.getElementById('material-number').value.trim();
                    if (!materialNumber) {
                        showFeedback('请先填写物料料号！', 'error');
                        return;
                    }
                    
                    const dt = e.dataTransfer;
                    const files = dt.files;
                    
                    if (files.length) {
                        fileInput.files = files;
                        
                        // 触发change事件
                        const event = new Event('change', { bubbles: true });
                        fileInput.dispatchEvent(event);
                        
                        showFeedback('图片已成功拖放上传', 'success');
                    }
                });
                
                // 7. 删除按钮事件
                const deleteBtn = box.querySelector('.delete-btn');
                if (deleteBtn) {
                    deleteBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        const questionNumber = this.getAttribute('data-question');
                        const previewImage = box.querySelector('.preview-image');
                        const uploadOverlay = box.querySelector('.upload-overlay');
                        
                        // 获取物料料号
                        const materialNumber = document.getElementById('material-number').value;
                        
                        // 检查是否有临时ID（已上传到服务器的图片）
                        const tempId = fileInput.getAttribute('data-temp-id');
                        
                        // 对于问题点7-18，添加确认对话框
                        if (questionNum >= 7 && questionNum <= 18) {
                            if (!confirm('确定要删除图片吗？')) {
                                return; // 用户取消删除操作
                            }
                        }
                        
                        if (tempId) {
                            // 从服务器删除图片
                            fetch('/Material_Sample_Confirmation_Form/delete_image', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    question_id: questionNumber,
                                    material_number: materialNumber,
                                    temp_id: tempId // 添加临时ID作为参数
                                })
                            })
                            .then(response => {
                                // 即使服务器返回错误也清除本地图片
                                if (!response.ok) {
                                    console.warn('删除图片服务器响应异常:', response.status);
                                    // 继续清除本地图片
                                    clearImageBox();
                                    showFeedback('图片已从本地移除', 'info');
                                    return null; // 不再尝试解析JSON
                                }
                                return response.json();
                            })
                            .then(data => {
                                if (data && data.success) {
                                    // 清除图片
                                    clearImageBox();
                                    showFeedback('图片已成功删除', 'success');
                                } else if (data) {
                                    // 服务器返回了错误但仍然清除本地图片
                                    clearImageBox();
                                    showFeedback(data.message || '图片已从本地移除', 'info');
                                }
                            })
                            .catch(error => {
                                console.error('删除图片错误:', error);
                                // 即使出错也清除本地图片
                                clearImageBox();
                                showFeedback('图片已从本地移除', 'info');
                            });
                        } else {
                            // 直接清除本地预览的图片
                            clearImageBox();
                            showFeedback('图片已删除', 'info');
                        }
                        
                        // 清除上传框的函数
                        function clearImageBox() {
                            previewImage.src = '';
                            previewImage.style.display = 'none';
                            uploadOverlay.style.display = 'block';
                            deleteBtn.style.display = 'none';
                            box.classList.remove('has-image');
                            
                            // 清除文件输入框的值
                            fileInput.value = '';
                            fileInput.removeAttribute('data-temp-id');
                        }
                    });
                }
            });
        }

        // 添加额外的初始化代码，确保按钮功能正常
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有复制按钮
            document.querySelectorAll('.copy-button').forEach(button => {
                if (button.hasAttribute('data-event-bound')) return;
                const box = button.closest('.image-upload-box');
                bindCopyButtonEvents(button, box);
            });
            
            // 初始化所有上传按钮
            document.querySelectorAll('.upload-button').forEach(button => {
                if (button.hasAttribute('data-event-bound')) return;
                const box = button.closest('.image-upload-box');
                const fileInput = box ? box.querySelector('.image-input') : null;
                bindUploadButtonEvents(button, fileInput);
            });
            
            // 初始化所有粘贴按钮
            document.querySelectorAll('.paste-button').forEach(button => {
                if (button.hasAttribute('data-event-bound')) return;
                const box = button.closest('.image-upload-box');
                const fileInput = box ? box.querySelector('.image-input') : null;
                bindPasteButtonEvents(button, box, fileInput);
            });
        });
        
        // 抽取上传按钮事件绑定函数
        function bindUploadButtonEvents(button, fileInput) {
            if (!button || button.hasAttribute('data-event-bound')) return;
            
            button.setAttribute('data-event-bound', 'true');
            
            // 添加悬停效果
            button.addEventListener('mouseover', function() {
                this.innerHTML = '📂 上传';
                this.style.width = '70px';
                this.style.paddingLeft = '8px';
            });
            
            button.addEventListener('mouseout', function() {
                this.innerHTML = '📂';
                this.style.width = '28px';
                this.style.paddingLeft = '4px';
            });
            
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                console.log('上传按钮被点击');
                
                // 检查物料编码
                const materialNumber = document.getElementById('material-number').value.trim();
                if (!materialNumber) {
                    showFeedback('请先填写物料料号！', 'error');
                    return;
                }
                
                // 获取上传框和文件输入
                const box = this.closest('.image-upload-box');
                const input = fileInput || box.querySelector('.image-input');
                
                if (input) {
                    console.log('触发文件选择', input);
                    // 触发文件选择
                    input.click();
                } else {
                    console.error('未找到文件输入控件');
                    showFeedback('未找到文件输入控件，请刷新页面重试', 'error');
                }
            });
        }
        
        // 添加复制按钮事件绑定函数
        function bindCopyButtonEvents(button, box) {
            if (!button || button.hasAttribute('data-event-bound')) return;
            
            button.setAttribute('data-event-bound', 'true');
            
            // 添加悬停效果
            button.addEventListener('mouseover', function() {
                this.innerHTML = '📑 复制';
                this.style.width = '70px';
                this.style.paddingLeft = '8px';
            });
            
            button.addEventListener('mouseout', function() {
                this.innerHTML = '📑';
                this.style.width = '28px';
                this.style.paddingLeft = '4px';
            });
            
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                console.log('复制按钮被点击');
                
                // 获取预览图片
                const previewImage = box.querySelector('.preview-image');
                if (!previewImage || !previewImage.src || previewImage.src === window.location.href) {
                    showFeedback('没有图片可复制', 'error');
                    return;
                }
                
                // 复制图片到剪贴板
                try {
                    copyImageToClipboard(previewImage);
                } catch (error) {
                    console.error('复制图片失败:', error);
                    showFeedback('复制图片失败，浏览器可能不支持此功能', 'error');
                }
            });
        }
        
        // 添加粘贴按钮事件绑定函数
        function bindPasteButtonEvents(button, box, fileInput) {
            if (!button || button.hasAttribute('data-event-bound')) return;
            
            button.setAttribute('data-event-bound', 'true');
            
            // 添加悬停效果
            button.addEventListener('mouseover', function() {
                this.innerHTML = '📋 粘贴';
                this.style.width = '70px';
                this.style.paddingLeft = '8px';
            });
            
            button.addEventListener('mouseout', function() {
                this.innerHTML = '📋';
                this.style.width = '28px';
                this.style.paddingLeft = '4px';
            });
            
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                console.log('粘贴按钮被点击');
                
                // 检查物料编码
                const materialNumber = document.getElementById('material-number').value.trim();
                if (!materialNumber) {
                    showFeedback('请先填写物料料号！', 'error');
                    return;
                }
                
                // 设置当前活动框
                window.lastClickedBox = box;
                window.lastHoveredBox = box;
                
                // 尝试读取剪贴板
                navigator.clipboard.read()
                    .then(clipboardItems => {
                        let hasImage = false;
                        
                        for (const item of clipboardItems) {
                            for (const type of item.types) {
                                if (type.startsWith('image/')) {
                                    hasImage = true;
                                    item.getType(type).then(blob => {
                                        const file = new File([blob], 'clipboard-image.png', { type });
                                        
                                        // 设置到文件输入框
                                        const dataTransfer = new DataTransfer();
                                        dataTransfer.items.add(file);
                                        fileInput.files = dataTransfer.files;
                                        
                                        // 触发change事件
                                        const event = new Event('change', { bubbles: true });
                                        fileInput.dispatchEvent(event);
                                        
                                        showFeedback('图片已成功粘贴', 'success');
                                    });
                                    break;
                                }
                            }
                            if (hasImage) break;
                        }
                        
                        if (!hasImage) {
                            showFeedback('剪贴板中没有图片', 'error');
                        }
                    })
                    .catch(err => {
                        console.error('无法访问剪贴板:', err);
                        showFeedback('无法访问剪贴板，请使用上传按钮', 'error');
                    });
            });
        }
    </script>
</body>
</html>